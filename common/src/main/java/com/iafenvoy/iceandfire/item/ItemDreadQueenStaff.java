package com.iafenvoy.iceandfire.item;

import com.iafenvoy.iceandfire.entity.EntityDreadBeast;
import com.iafenvoy.iceandfire.entity.EntityDreadGhoul;
import com.iafenvoy.iceandfire.entity.EntityDreadLich;
import com.iafenvoy.iceandfire.entity.EntityDreadMob;
import com.iafenvoy.iceandfire.entity.EntityDreadScuttler;
import com.iafenvoy.iceandfire.entity.EntityDreadThrall;
import com.iafenvoy.iceandfire.entity.ai.DreadMinionMode;
import com.iafenvoy.iceandfire.registry.IafEntities;
import com.iafenvoy.uranus.object.RegistryHelper;
import net.minecraft.enchantment.EnchantmentHelper;
import net.minecraft.entity.EquipmentSlot;
import net.minecraft.entity.LivingEntity;
import net.minecraft.entity.SpawnReason;
import net.minecraft.entity.mob.MobEntity;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.item.Item;
import net.minecraft.item.ItemStack;
import net.minecraft.registry.Registries;
import net.minecraft.registry.RegistryKeys;
import net.minecraft.server.world.ServerWorld;
import net.minecraft.sound.SoundEvents;
import net.minecraft.text.Text;
import net.minecraft.util.ActionResult;
import net.minecraft.util.Hand;
import net.minecraft.util.Identifier;
import net.minecraft.util.TypedActionResult;
import net.minecraft.util.math.BlockPos;
import net.minecraft.world.ServerWorldAccess;
import net.minecraft.world.World;

import java.util.List;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import net.minecraft.util.math.random.Random;

public class ItemDreadQueenStaff extends Item {
    // 冷却时间映射 (玩家UUID -> 冷却结束时间)
    private static final Map<UUID, Long> COOLDOWNS = new HashMap<>();
    private static final int COOLDOWN_TICKS = 200; // 10秒冷却 (20 ticks = 1秒)

    public ItemDreadQueenStaff() {
        super(new Settings().maxCount(1).maxDamage(500));
    }

    @Override
    public TypedActionResult<ItemStack> use(World world, PlayerEntity player, Hand hand) {
        ItemStack stack = player.getStackInHand(hand);

        if (world.isClient) {
            return TypedActionResult.success(stack);
        }

        // 检查玩家是否穿着异界统御附魔的头盔
        if (!hasOtherworldDominion(player)) {
            player.sendMessage(Text.translatable("item.iceandfire.dread_queen_staff.no_enchantment"), true);
            return TypedActionResult.fail(stack);
        }

        if (player.isSneaking()) {
            // 蹲下右键：切换召唤生物的AI模式（不消耗冷却）
            switchMinionMode(world, player);
        } else {
            // 检查冷却时间（只对召唤功能）
            if (isOnCooldown(player)) {
                long remainingTicks = getRemainingCooldown(player);
                long remainingSeconds = remainingTicks / 20;
                player.sendMessage(Text.translatable("item.iceandfire.dread_queen_staff.cooldown", remainingSeconds), true);
                return TypedActionResult.fail(stack);
            }

            // 普通右键：召唤惊悚尸巫
            player.sendMessage(Text.literal("开始召唤..."), false);
            summonDreadLiches(world, player, stack);
            // 设置冷却时间
            setCooldown(player);
        }

        return TypedActionResult.success(stack);
    }

    private boolean isOnCooldown(PlayerEntity player) {
        Long cooldownEnd = COOLDOWNS.get(player.getUuid());
        if (cooldownEnd == null) return false;
        return player.getWorld().getTime() < cooldownEnd;
    }

    private long getRemainingCooldown(PlayerEntity player) {
        Long cooldownEnd = COOLDOWNS.get(player.getUuid());
        if (cooldownEnd == null) return 0;
        return Math.max(0, cooldownEnd - player.getWorld().getTime());
    }

    private void setCooldown(PlayerEntity player) {
        COOLDOWNS.put(player.getUuid(), player.getWorld().getTime() + COOLDOWN_TICKS);
    }

    private boolean hasOtherworldDominion(PlayerEntity player) {
        ItemStack helmet = player.getEquippedStack(EquipmentSlot.HEAD);
        if (helmet.isEmpty()) return false;

        try {
            var enchantmentRegistry = player.getRegistryManager().get(RegistryKeys.ENCHANTMENT);
            var enchantmentEntry = enchantmentRegistry.getEntry(Identifier.of("iceandfire", "otherworld_dominion"));
            if (enchantmentEntry.isEmpty()) return false;

            return EnchantmentHelper.getLevel(enchantmentEntry.get(), helmet) > 0;
        } catch (Exception e) {
            return false;
        }
    }

    private void summonDreadLiches(World world, PlayerEntity player, ItemStack stack) {
        if (!(world instanceof ServerWorld serverWorld)) {
            return; // 只在服务器端执行
        }

        Random random = world.getRandom();
        int lichCount = 1 + random.nextInt(3); // 1-3个惊悚尸巫

        for (int i = 0; i < lichCount; i++) {
            EntityDreadLich lich = new EntityDreadLich(IafEntities.DREAD_LICH.get(), world);

            // 在玩家周围随机位置生成
            double angle = random.nextDouble() * 2 * Math.PI;
            double distance = 2 + random.nextDouble() * 3; // 2-5格距离
            double x = player.getX() + Math.cos(angle) * distance;
            double z = player.getZ() + Math.sin(angle) * distance;

            // 找到合适的Y坐标
            BlockPos spawnPos = new BlockPos((int)x, (int)player.getY(), (int)z);
            double y = player.getY();

            // 尝试找到地面
            for (int yOffset = -5; yOffset <= 10; yOffset++) {
                BlockPos checkPos = spawnPos.add(0, yOffset, 0);
                if (world.getBlockState(checkPos).isAir() &&
                    world.getBlockState(checkPos.down()).isSolidBlock(world, checkPos.down())) {
                    y = checkPos.getY();
                    break;
                }
            }

            lich.refreshPositionAndAngles(x, y, z, random.nextFloat() * 360, 0);
            lich.initialize(serverWorld, world.getLocalDifficulty(spawnPos), SpawnReason.MOB_SUMMONED, null);

            // 设置为玩家的召唤物
            lich.setCommanderId(player.getUuid());

            // 移除时间限制，让召唤生物永久存在
            // scheduleDespawn(lich, 600); // 已移除时间限制

            // 生成实体
            boolean spawned = world.spawnEntity(lich);

            if (spawned) {
                // 每个尸巫召唤1-10个惊悚生物
                summonDreadMinions(world, lich, player);
            }
        }

        // 消耗耐久度
        stack.damage(10, player, EquipmentSlot.MAINHAND);

        // 播放音效
        player.playSound(SoundEvents.ENTITY_WITHER_SPAWN, 1.0F, 0.8F);

        player.sendMessage(Text.translatable("item.iceandfire.dread_queen_staff.summoned", lichCount), true);
    }

    private void summonDreadMinions(World world, EntityDreadLich lich, PlayerEntity player) {
        if (!(world instanceof ServerWorld serverWorld)) {
            return; // 只在服务器端执行
        }

        Random random = world.getRandom();
        int minionCount = 1 + random.nextInt(10); // 1-10个惊悚生物

        for (int i = 0; i < minionCount; i++) {
            MobEntity minion = createRandomDreadMinion(world, random);
            if (minion == null) continue;

            // 在尸巫周围生成
            double angle = random.nextDouble() * 2 * Math.PI;
            double distance = 1 + random.nextDouble() * 2; // 1-3格距离
            double x = lich.getX() + Math.cos(angle) * distance;
            double z = lich.getZ() + Math.sin(angle) * distance;

            // 找到合适的Y坐标
            BlockPos spawnPos = new BlockPos((int)x, (int)lich.getY(), (int)z);
            double y = lich.getY();

            // 尝试找到地面
            for (int yOffset = -3; yOffset <= 5; yOffset++) {
                BlockPos checkPos = spawnPos.add(0, yOffset, 0);
                if (world.getBlockState(checkPos).isAir() &&
                    world.getBlockState(checkPos.down()).isSolidBlock(world, checkPos.down())) {
                    y = checkPos.getY();
                    break;
                }
            }

            minion.refreshPositionAndAngles(x, y, z, random.nextFloat() * 360, 0);
            minion.initialize(serverWorld, world.getLocalDifficulty(spawnPos), SpawnReason.MOB_SUMMONED, null);

            // 设置为玩家的召唤物
            if (minion instanceof EntityDreadMob dreadMob) {
                dreadMob.setCommanderId(player.getUuid());
            }

            // 移除时间限制，让召唤生物永久存在
            // scheduleDespawn(minion, 600); // 已移除时间限制

            world.spawnEntity(minion);
        }
    }

    private MobEntity createRandomDreadMinion(World world, Random random) {
        int type = random.nextInt(4);
        return switch (type) {
            case 0 -> new EntityDreadThrall(IafEntities.DREAD_THRALL.get(), world);
            case 1 -> new EntityDreadGhoul(IafEntities.DREAD_GHOUL.get(), world);
            case 2 -> new EntityDreadBeast(IafEntities.DREAD_BEAST.get(), world);
            case 3 -> new EntityDreadScuttler(IafEntities.DREAD_SCUTTLER.get(), world);
            default -> null;
        };
    }

    private void switchMinionMode(World world, PlayerEntity player) {
        // 查找玩家周围的召唤生物
        List<EntityDreadMob> minions = world.getEntitiesByClass(EntityDreadMob.class,
            player.getBoundingBox().expand(15),
            mob -> player.getUuid().equals(mob.getCommanderId()));

        if (minions.isEmpty()) {
            player.sendMessage(Text.translatable("item.iceandfire.dread_queen_staff.no_minions"), true);
            return;
        }

        // 获取第一个生物的当前模式，然后切换到下一个模式
        DreadMinionMode currentMode = minions.get(0).getMinionMode();
        DreadMinionMode nextMode = currentMode.next();

        // 为所有召唤生物设置新模式
        for (EntityDreadMob minion : minions) {
            minion.setMinionMode(nextMode);
        }

        // 发送模式切换消息
        String modeKey = "item.iceandfire.dread_queen_staff.mode." + nextMode.asString();
        player.sendMessage(Text.translatable("item.iceandfire.dread_queen_staff.mode_switched_to",
            Text.translatable(modeKey), minions.size()), true);
    }

    private void scheduleDespawn(MobEntity entity, int ticks) {
        // 移除自动消失逻辑，让召唤生物永久存在
        entity.setCustomName(Text.literal("召唤生物"));
        entity.setCustomNameVisible(false);

        // 不再设置生存时间，召唤生物将永久存在直到被击杀
        // if (entity instanceof EntityDreadMob dreadMob) {
        //     dreadMob.setSummonedLifetime(ticks); // 已移除
        // }
    }
}
