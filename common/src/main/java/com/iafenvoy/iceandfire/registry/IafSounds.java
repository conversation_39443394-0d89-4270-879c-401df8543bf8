package com.iafenvoy.iceandfire.registry;

import com.iafenvoy.iceandfire.IceAndFire;
import dev.architectury.registry.registries.DeferredRegister;
import dev.architectury.registry.registries.RegistrySupplier;
import net.minecraft.registry.RegistryKeys;
import net.minecraft.sound.SoundEvent;
import net.minecraft.util.Identifier;

@SuppressWarnings("unused")
public final class IafSounds {
    public static final DeferredRegister<SoundEvent> REGISTRY = DeferredRegister.create(IceAndFire.MOD_ID, RegistryKeys.SOUND_EVENT);

    public static final RegistrySupplier<SoundEvent> BESTIARY_PAGE = of("bestiary_page");
    public static final RegistrySupplier<SoundEvent> EGG_HATCH = of("egg_hatch");
    public static final RegistrySupplier<SoundEvent> FIREDRAGON_BREATH = of("firedragon_breath");
    public static final RegistrySupplier<SoundEvent> ICEDRAGON_BREATH = of("icedragon_breath");
    public static final RegistrySupplier<SoundEvent> FIREDRAGON_CHILD_IDLE = of("firedragon_child_idle");
    public static final RegistrySupplier<SoundEvent> FIREDRAGON_CHILD_HURT = of("firedragon_child_hurt");
    public static final RegistrySupplier<SoundEvent> FIREDRAGON_CHILD_DEATH = of("firedragon_child_death");
    public static final RegistrySupplier<SoundEvent> FIREDRAGON_CHILD_ROAR = of("firedragon_child_roar");
    public static final RegistrySupplier<SoundEvent> FIREDRAGON_TEEN_ROAR = of("firedragon_teen_roar");
    public static final RegistrySupplier<SoundEvent> FIREDRAGON_TEEN_IDLE = of("firedragon_teen_idle");
    public static final RegistrySupplier<SoundEvent> FIREDRAGON_TEEN_DEATH = of("firedragon_teen_death");
    public static final RegistrySupplier<SoundEvent> FIREDRAGON_TEEN_HURT = of("firedragon_teen_hurt");
    public static final RegistrySupplier<SoundEvent> FIREDRAGON_ADULT_ROAR = of("firedragon_adult_roar");
    public static final RegistrySupplier<SoundEvent> FIREDRAGON_ADULT_IDLE = of("firedragon_adult_idle");
    public static final RegistrySupplier<SoundEvent> FIREDRAGON_ADULT_DEATH = of("firedragon_adult_death");
    public static final RegistrySupplier<SoundEvent> FIREDRAGON_ADULT_HURT = of("firedragon_adult_hurt");
    public static final RegistrySupplier<SoundEvent> ICEDRAGON_CHILD_IDLE = of("icedragon_child_idle");
    public static final RegistrySupplier<SoundEvent> ICEDRAGON_CHILD_HURT = of("icedragon_child_hurt");
    public static final RegistrySupplier<SoundEvent> ICEDRAGON_CHILD_DEATH = of("icedragon_child_death");
    public static final RegistrySupplier<SoundEvent> ICEDRAGON_CHILD_ROAR = of("icedragon_child_roar");
    public static final RegistrySupplier<SoundEvent> ICEDRAGON_TEEN_ROAR = of("icedragon_teen_roar");
    public static final RegistrySupplier<SoundEvent> ICEDRAGON_TEEN_IDLE = of("icedragon_teen_idle");
    public static final RegistrySupplier<SoundEvent> ICEDRAGON_TEEN_DEATH = of("icedragon_teen_death");
    public static final RegistrySupplier<SoundEvent> ICEDRAGON_TEEN_HURT = of("icedragon_teen_hurt");
    public static final RegistrySupplier<SoundEvent> ICEDRAGON_ADULT_ROAR = of("icedragon_adult_roar");
    public static final RegistrySupplier<SoundEvent> ICEDRAGON_ADULT_IDLE = of("icedragon_adult_idle");
    public static final RegistrySupplier<SoundEvent> ICEDRAGON_ADULT_DEATH = of("icedragon_adult_death");
    public static final RegistrySupplier<SoundEvent> ICEDRAGON_ADULT_HURT = of("icedragon_adult_hurt");
    public static final RegistrySupplier<SoundEvent> DRAGONFLUTE = of("dragonflute");
    public static final RegistrySupplier<SoundEvent> HIPPOGRYPH_IDLE = of("hippogryph_idle");
    public static final RegistrySupplier<SoundEvent> HIPPOGRYPH_HURT = of("hippogryph_hurt");
    public static final RegistrySupplier<SoundEvent> HIPPOGRYPH_DIE = of("hippogryph_die");
    public static final RegistrySupplier<SoundEvent> GORGON_IDLE = of("gorgon_idle");
    public static final RegistrySupplier<SoundEvent> GORGON_HURT = of("gorgon_hurt");
    public static final RegistrySupplier<SoundEvent> GORGON_DIE = of("gorgon_die");
    public static final RegistrySupplier<SoundEvent> GORGON_ATTACK = of("gorgon_attack");
    public static final RegistrySupplier<SoundEvent> GORGON_PETRIFY = of("gorgon_petrify");
    public static final RegistrySupplier<SoundEvent> TURN_STONE = of("turn_stone");
    public static final RegistrySupplier<SoundEvent> PIXIE_IDLE = of("pixie_idle");
    public static final RegistrySupplier<SoundEvent> PIXIE_HURT = of("pixie_hurt");
    public static final RegistrySupplier<SoundEvent> PIXIE_DIE = of("pixie_die");
    public static final RegistrySupplier<SoundEvent> PIXIE_TAUNT = of("pixie_taunt");
    public static final RegistrySupplier<SoundEvent> GOLD_PILE_STEP = of("gold_pile_step");
    public static final RegistrySupplier<SoundEvent> GOLD_PILE_BREAK = of("gold_pile_break");
    public static final RegistrySupplier<SoundEvent> DRAGON_FLIGHT = of("dragon_flight");
    public static final RegistrySupplier<SoundEvent> CYCLOPS_IDLE = of("cyclops_idle");
    public static final RegistrySupplier<SoundEvent> CYCLOPS_HURT = of("cyclops_hurt");
    public static final RegistrySupplier<SoundEvent> CYCLOPS_DIE = of("cyclops_die");
    public static final RegistrySupplier<SoundEvent> CYCLOPS_BITE = of("cyclops_bite");
    public static final RegistrySupplier<SoundEvent> CYCLOPS_BLINDED = of("cyclops_blinded");
    public static final RegistrySupplier<SoundEvent> HIPPOCAMPUS_IDLE = of("hippocampus_idle");
    public static final RegistrySupplier<SoundEvent> HIPPOCAMPUS_HURT = of("hippocampus_hurt");
    public static final RegistrySupplier<SoundEvent> HIPPOCAMPUS_DIE = of("hippocampus_die");
    public static final RegistrySupplier<SoundEvent> DEATHWORM_IDLE = of("deathworm_idle");
    public static final RegistrySupplier<SoundEvent> DEATHWORM_ATTACK = of("deathworm_attack");
    public static final RegistrySupplier<SoundEvent> DEATHWORM_HURT = of("deathworm_hurt");
    public static final RegistrySupplier<SoundEvent> DEATHWORM_DIE = of("deathworm_die");
    public static final RegistrySupplier<SoundEvent> DEATHWORM_GIANT_IDLE = of("deathworm_giant_idle");
    public static final RegistrySupplier<SoundEvent> DEATHWORM_GIANT_ATTACK = of("deathworm_giant_attack");
    public static final RegistrySupplier<SoundEvent> DEATHWORM_GIANT_HURT = of("deathworm_giant_hurt");
    public static final RegistrySupplier<SoundEvent> DEATHWORM_GIANT_DIE = of("deathworm_giant_die");
    public static final RegistrySupplier<SoundEvent> NAGA_IDLE = of("naga_idle");
    public static final RegistrySupplier<SoundEvent> NAGA_ATTACK = of("naga_attack");
    public static final RegistrySupplier<SoundEvent> NAGA_HURT = of("naga_hurt");
    public static final RegistrySupplier<SoundEvent> NAGA_DIE = of("naga_die");
    public static final RegistrySupplier<SoundEvent> MERMAID_IDLE = of("mermaid_idle");
    public static final RegistrySupplier<SoundEvent> MERMAID_HURT = of("mermaid_hurt");
    public static final RegistrySupplier<SoundEvent> MERMAID_DIE = of("mermaid_die");
    public static final RegistrySupplier<SoundEvent> SIREN_SONG = of("siren_song");
    public static final RegistrySupplier<SoundEvent> TROLL_DIE = of("troll_die");
    public static final RegistrySupplier<SoundEvent> TROLL_IDLE = of("troll_idle");
    public static final RegistrySupplier<SoundEvent> TROLL_HURT = of("troll_hurt");
    public static final RegistrySupplier<SoundEvent> TROLL_ROAR = of("troll_roar");
    public static final RegistrySupplier<SoundEvent> COCKATRICE_DIE = of("cockatrice_die");
    public static final RegistrySupplier<SoundEvent> COCKATRICE_IDLE = of("cockatrice_idle");
    public static final RegistrySupplier<SoundEvent> COCKATRICE_HURT = of("cockatrice_hurt");
    public static final RegistrySupplier<SoundEvent> COCKATRICE_CRY = of("cockatrice_cry");
    public static final RegistrySupplier<SoundEvent> STYMPHALIAN_BIRD_DIE = of("stymphalian_bird_die");
    public static final RegistrySupplier<SoundEvent> STYMPHALIAN_BIRD_IDLE = of("stymphalian_bird_idle");
    public static final RegistrySupplier<SoundEvent> STYMPHALIAN_BIRD_HURT = of("stymphalian_bird_hurt");
    public static final RegistrySupplier<SoundEvent> STYMPHALIAN_BIRD_ATTACK = of("stymphalian_bird_attack");
    public static final RegistrySupplier<SoundEvent> AMPHITHERE_DIE = of("amphithere_die");
    public static final RegistrySupplier<SoundEvent> AMPHITHERE_IDLE = of("amphithere_idle");
    public static final RegistrySupplier<SoundEvent> AMPHITHERE_HURT = of("amphithere_hurt");
    public static final RegistrySupplier<SoundEvent> AMPHITHERE_BITE = of("amphithere_bite");
    public static final RegistrySupplier<SoundEvent> AMPHITHERE_GUST = of("amphithere_gust");
    public static final RegistrySupplier<SoundEvent> SEA_SERPENT_DIE = of("sea_serpent_die");
    public static final RegistrySupplier<SoundEvent> SEA_SERPENT_IDLE = of("sea_serpent_idle");
    public static final RegistrySupplier<SoundEvent> SEA_SERPENT_HURT = of("sea_serpent_hurt");
    public static final RegistrySupplier<SoundEvent> SEA_SERPENT_BITE = of("sea_serpent_bite");
    public static final RegistrySupplier<SoundEvent> SEA_SERPENT_ROAR = of("sea_serpent_roar");
    public static final RegistrySupplier<SoundEvent> SEA_SERPENT_BREATH = of("sea_serpent_breath");
    public static final RegistrySupplier<SoundEvent> SEA_SERPENT_SPLASH = of("sea_serpent_splash");
    public static final RegistrySupplier<SoundEvent> HYDRA_DIE = of("hydra_die");
    public static final RegistrySupplier<SoundEvent> HYDRA_IDLE = of("hydra_idle");
    public static final RegistrySupplier<SoundEvent> HYDRA_HURT = of("hydra_hurt");
    public static final RegistrySupplier<SoundEvent> HYDRA_SPIT = of("hydra_spit");
    public static final RegistrySupplier<SoundEvent> HYDRA_REGEN_HEAD = of("hydra_regen_head");
    public static final RegistrySupplier<SoundEvent> PIXIE_WAND = of("pixie_wand");
    public static final RegistrySupplier<SoundEvent> DREAD_LICH_SUMMON = of("dread_lich_summon");
    public static final RegistrySupplier<SoundEvent> DREAD_GHOUL_IDLE = of("dread_ghoul_idle");
    public static final RegistrySupplier<SoundEvent> LIGHTNINGDRAGON_CHILD_IDLE = of("lightningdragon_child_idle");
    public static final RegistrySupplier<SoundEvent> LIGHTNINGDRAGON_CHILD_HURT = of("lightningdragon_child_hurt");
    public static final RegistrySupplier<SoundEvent> LIGHTNINGDRAGON_CHILD_DEATH = of("lightningdragon_child_death");
    public static final RegistrySupplier<SoundEvent> LIGHTNINGDRAGON_CHILD_ROAR = of("lightningdragon_child_roar");
    public static final RegistrySupplier<SoundEvent> LIGHTNINGDRAGON_TEEN_ROAR = of("lightningdragon_teen_roar");
    public static final RegistrySupplier<SoundEvent> LIGHTNINGDRAGON_TEEN_IDLE = of("lightningdragon_teen_idle");
    public static final RegistrySupplier<SoundEvent> LIGHTNINGDRAGON_TEEN_DEATH = of("lightningdragon_teen_death");
    public static final RegistrySupplier<SoundEvent> LIGHTNINGDRAGON_TEEN_HURT = of("lightningdragon_teen_hurt");
    public static final RegistrySupplier<SoundEvent> LIGHTNINGDRAGON_ADULT_ROAR = of("lightningdragon_adult_roar");
    public static final RegistrySupplier<SoundEvent> LIGHTNINGDRAGON_ADULT_IDLE = of("lightningdragon_adult_idle");
    public static final RegistrySupplier<SoundEvent> LIGHTNINGDRAGON_ADULT_DEATH = of("lightningdragon_adult_death");
    public static final RegistrySupplier<SoundEvent> LIGHTNINGDRAGON_ADULT_HURT = of("lightningdragon_adult_hurt");
    public static final RegistrySupplier<SoundEvent> LIGHTNINGDRAGON_BREATH = of("lightningdragon_breath");
    public static final RegistrySupplier<SoundEvent> LIGHTNINGDRAGON_BREATH_CRACKLE = of("lightningdragon_breath_crackle");
    public static final RegistrySupplier<SoundEvent> GHOST_IDLE = of("ghost_idle");
    public static final RegistrySupplier<SoundEvent> GHOST_HURT = of("ghost_hurt");
    public static final RegistrySupplier<SoundEvent> GHOST_DIE = of("ghost_die");
    public static final RegistrySupplier<SoundEvent> GHOST_ATTACK = of("ghost_attack");
    public static final RegistrySupplier<SoundEvent> GHOST_JUMPSCARE = of("ghost_jumpscare");

    private static RegistrySupplier<SoundEvent> of(String soundName) {
        return REGISTRY.register(soundName, () -> SoundEvent.of(Identifier.of(IceAndFire.MOD_ID, soundName)));
    }
}
