package com.iafenvoy.iceandfire.render.model;

import com.google.common.collect.ImmutableList;
import com.iafenvoy.uranus.client.model.AdvancedEntityModel;
import com.iafenvoy.uranus.client.model.AdvancedModelBox;
import com.iafenvoy.uranus.client.model.basic.BasicModelPart;
import net.minecraft.entity.Entity;

public class ModelGorgonHeadActive extends AdvancedEntityModel<Entity> {
    public final AdvancedModelBox Head;
    public final AdvancedModelBox Head_Details;
    public final AdvancedModelBox SnakeBaseR2;
    public final AdvancedModelBox SnakeBaseR7;
    public final AdvancedModelBox SnakeBaseR6;
    public final AdvancedModelBox SnakeBaseR5;
    public final AdvancedModelBox SnakeBaseR4;
    public final AdvancedModelBox SnakeBaseR3;
    public final AdvancedModelBox SnakeBaseR1;
    public final AdvancedModelBox SnakeBaseL1;
    public final AdvancedModelBox SnakeBaseL2;
    public final AdvancedModelBox SnakeBaseL4;
    public final AdvancedModelBox SnakeBaseL3;
    public final AdvancedModelBox SnakeBaseL7;
    public final AdvancedModelBox SnakeBaseL6;
    public final AdvancedModelBox SnakeBaseL5;
    public final AdvancedModelBox SnakeBodyR2;
    public final AdvancedModelBox SnakeHeadR2;
    public final AdvancedModelBox SnakeJawR2;
    public final AdvancedModelBox SnakeFang1R2;
    public final AdvancedModelBox SnakeFang2R2;
    public final AdvancedModelBox SnakeBodyR7;
    public final AdvancedModelBox SnakeHeadR7;
    public final AdvancedModelBox SnakeJawR7;
    public final AdvancedModelBox SnakeFang1R7;
    public final AdvancedModelBox SnakeFang2R7;
    public final AdvancedModelBox SnakeBodyR6;
    public final AdvancedModelBox SnakeHeadR6;
    public final AdvancedModelBox SnakeJawR6;
    public final AdvancedModelBox SnakeFang1R6;
    public final AdvancedModelBox SnakeFang2R6;
    public final AdvancedModelBox SnakeBodyR5;
    public final AdvancedModelBox SnakeHeadR5;
    public final AdvancedModelBox SnakeJawR5;
    public final AdvancedModelBox SnakeFang1R5;
    public final AdvancedModelBox SnakeFang2R5;
    public final AdvancedModelBox SnakeBodyR4;
    public final AdvancedModelBox SnakeHeadR4;
    public final AdvancedModelBox SnakeJawR4;
    public final AdvancedModelBox SnakeFang1R4;
    public final AdvancedModelBox SnakeFang2R4;
    public final AdvancedModelBox SnakeBodyR3;
    public final AdvancedModelBox SnakeHeadR3;
    public final AdvancedModelBox SnakeJawR3;
    public final AdvancedModelBox SnakeFang1R3;
    public final AdvancedModelBox SnakeFang2R3;
    public final AdvancedModelBox SnakeBodyR1;
    public final AdvancedModelBox SnakeHeadR1;
    public final AdvancedModelBox SnakeJawR1;
    public final AdvancedModelBox SnakeFang1R1;
    public final AdvancedModelBox SnakeFang2R1;
    public final AdvancedModelBox SnakeBodyL1;
    public final AdvancedModelBox SnakeHeadL1;
    public final AdvancedModelBox SnakeJawL1;
    public final AdvancedModelBox SnakeFang1L1;
    public final AdvancedModelBox SnakeFang2L1;
    public final AdvancedModelBox SnakeBodyL2;
    public final AdvancedModelBox SnakeHeadL2;
    public final AdvancedModelBox SnakeJawL2;
    public final AdvancedModelBox SnakeFang1L2;
    public final AdvancedModelBox SnakeFang2L2;
    public final AdvancedModelBox SnakeBodyL4;
    public final AdvancedModelBox SnakeHeadR4_1;
    public final AdvancedModelBox SnakeJawR4_1;
    public final AdvancedModelBox SnakeFang1R4_1;
    public final AdvancedModelBox SnakeFang2R4_1;
    public final AdvancedModelBox SnakeBodyL3;
    public final AdvancedModelBox SnakeHeadL3;
    public final AdvancedModelBox SnakeJawL3;
    public final AdvancedModelBox SnakeFang1L3;
    public final AdvancedModelBox SnakeFang2L3;
    public final AdvancedModelBox SnakeBodyL7;
    public final AdvancedModelBox SnakeHeadL7;
    public final AdvancedModelBox SnakeJawL7;
    public final AdvancedModelBox SnakeFang1L7;
    public final AdvancedModelBox SnakeFang2L7;
    public final AdvancedModelBox snakeBodyL6;
    public final AdvancedModelBox SnakeHeadL6;
    public final AdvancedModelBox SnakeJawL6;
    public final AdvancedModelBox SnakeFang1L6;
    public final AdvancedModelBox SnakeFang2L6;
    public final AdvancedModelBox SnakeBodyL5;
    public final AdvancedModelBox SnakeHeadL5;
    public final AdvancedModelBox SnakeJawL5;
    public final AdvancedModelBox SnakeFang1L5;
    public final AdvancedModelBox SnakeFang2L5;

    public ModelGorgonHeadActive() {
        this.texWidth = 128;
        this.texHeight = 128;
        this.SnakeJawR1 = new AdvancedModelBox(this, 15, 37);
        this.SnakeJawR1.setPos(0.0F, 0.11F, -0.3F);
        this.SnakeJawR1.addBox(-1.0F, -0.3F, -2.2F, 2, 1, 3, 0.0F);
        this.SnakeFang2R7 = new AdvancedModelBox(this, 31, 37);
        this.SnakeFang2R7.setPos(1.75F, -0.09F, -0.2F);
        this.SnakeFang2R7.addBox(-1.0F, -0.3F, -2.2F, 0, 1, 1, 0.0F);
        this.SnakeJawR3 = new AdvancedModelBox(this, 15, 37);
        this.SnakeJawR3.setPos(0.0F, 0.11F, -0.3F);
        this.SnakeJawR3.addBox(-1.0F, -0.3F, -2.2F, 2, 1, 3, 0.0F);
        this.SnakeHeadR5 = new AdvancedModelBox(this, 6, 36);
        this.SnakeHeadR5.setPos(0.0F, 0.1F, -4.3F);
        this.SnakeHeadR5.addBox(-1.0F, -1.3F, -2.5F, 2, 1, 3, 0.0F);
        this.setRotateAngle(this.SnakeHeadR5, 0.767944870877505F, -1.0170033551370958F, 0.8726646259971648F);
        this.SnakeFang1R4_1 = new AdvancedModelBox(this, 31, 37);
        this.SnakeFang1R4_1.setPos(0.25F, -0.09F, -0.2F);
        this.SnakeFang1R4_1.addBox(-1.0F, -0.3F, -2.2F, 0, 1, 1, 0.0F);
        this.SnakeHeadR2 = new AdvancedModelBox(this, 6, 36);
        this.SnakeHeadR2.setPos(0.0F, 0.1F, -4.3F);
        this.SnakeHeadR2.addBox(-1.0F, -1.3F, -2.5F, 2, 1, 3, 0.0F);
        this.setRotateAngle(this.SnakeHeadR2, 0.7749261878854823F, -0.0F, -0.17453292519943295F);
        this.SnakeFang2R6 = new AdvancedModelBox(this, 31, 37);
        this.SnakeFang2R6.setPos(1.75F, -0.09F, -0.2F);
        this.SnakeFang2R6.addBox(-1.0F, -0.3F, -2.2F, 0, 1, 1, 0.0F);
        this.SnakeBaseL4 = new AdvancedModelBox(this, 25, 50);
        this.SnakeBaseL4.setPos(1.0F, -6.5F, 3.3F);
        this.SnakeBaseL4.addBox(-0.5F, -1.0F, -5.8F, 1, 1, 6, 0.0F);
        this.setRotateAngle(this.SnakeBaseL4, -2.1282544898818854F, 0.0F, 0.2617993877991494F);
        this.SnakeBaseR5 = new AdvancedModelBox(this, 25, 50);
        this.SnakeBaseR5.setPos(-1.0F, -4.5F, 3.3F);
        this.SnakeBaseR5.addBox(-0.5F, -1.0F, -5.8F, 1, 1, 6, 0.0F);
        this.setRotateAngle(this.SnakeBaseR5, -2.199463923363254F, -0.15812683023068624F, -2.255838058202671F);
        this.SnakeBaseR2 = new AdvancedModelBox(this, 25, 50);
        this.SnakeBaseR2.setPos(-2.0F, -7.5F, -0.7F);
        this.SnakeBaseR2.addBox(-0.5F, -1.0F, -5.8F, 1, 1, 6, 0.0F);
        this.setRotateAngle(this.SnakeBaseR2, -1.5707963267948966F, 0.0F, -2.2689280275926285F);
        this.SnakeFang1R7 = new AdvancedModelBox(this, 31, 37);
        this.SnakeFang1R7.setPos(0.25F, -0.09F, -0.2F);
        this.SnakeFang1R7.addBox(-1.0F, -0.3F, -2.2F, 0, 1, 1, 0.0F);
        this.SnakeHeadR7 = new AdvancedModelBox(this, 6, 36);
        this.SnakeHeadR7.setPos(0.0F, 0.1F, -4.3F);
        this.SnakeHeadR7.addBox(-1.0F, -1.3F, -2.5F, 2, 1, 3, 0.0F);
        this.setRotateAngle(this.SnakeHeadR7, 0.8813912722571364F, -0.4293509959906051F, 0.8361872446304832F);
        this.SnakeFang2R4_1 = new AdvancedModelBox(this, 31, 37);
        this.SnakeFang2R4_1.setPos(1.75F, -0.09F, -0.2F);
        this.SnakeFang2R4_1.addBox(-1.0F, -0.3F, -2.2F, 0, 1, 1, 0.0F);
        this.SnakeBaseL2 = new AdvancedModelBox(this, 25, 50);
        this.SnakeBaseL2.setPos(2.0F, -7.5F, -0.7F);
        this.SnakeBaseL2.addBox(-0.5F, -1.0F, -5.8F, 1, 1, 6, 0.0F);
        this.setRotateAngle(this.SnakeBaseL2, -1.5707963267948966F, 0.0F, 2.2689280275926285F);
        this.SnakeFang1R3 = new AdvancedModelBox(this, 31, 37);
        this.SnakeFang1R3.setPos(0.25F, -0.09F, -0.2F);
        this.SnakeFang1R3.addBox(-1.0F, -0.3F, -2.2F, 0, 1, 1, 0.0F);
        this.SnakeHeadL6 = new AdvancedModelBox(this, 6, 36);
        this.SnakeHeadL6.setPos(0.0F, 0.1F, -4.3F);
        this.SnakeHeadL6.addBox(-1.0F, -1.3F, -2.5F, 2, 1, 3, 0.0F);
        this.setRotateAngle(this.SnakeHeadL6, 0.767944870877505F, 0.6108652381980153F, -0.8726646259971648F);
        this.SnakeFang1R1 = new AdvancedModelBox(this, 31, 37);
        this.SnakeFang1R1.setPos(0.25F, -0.09F, -0.2F);
        this.SnakeFang1R1.addBox(-1.0F, -0.3F, -2.2F, 0, 1, 1, 0.0F);
        this.SnakeHeadL5 = new AdvancedModelBox(this, 6, 36);
        this.SnakeHeadL5.setPos(0.0F, 0.1F, -4.3F);
        this.SnakeHeadL5.addBox(-1.0F, -1.3F, -2.5F, 2, 1, 3, 0.0F);
        this.setRotateAngle(this.SnakeHeadL5, 0.767944870877505F, 1.0170033551370958F, -0.8726646259971648F);
        this.SnakeBodyR3 = new AdvancedModelBox(this, 43, 52);
        this.SnakeBodyR3.setPos(0.0F, 0.4F, -5.6F);
        this.SnakeBodyR3.addBox(-0.5F, -1.0F, -5.0F, 1, 1, 6, 0.0F);
        this.setRotateAngle(this.SnakeBodyR3, 1.0622073827637488F, -0.7457791893771769F, 0.0F);
        this.SnakeHeadL7 = new AdvancedModelBox(this, 6, 36);
        this.SnakeHeadL7.setPos(0.0F, 0.1F, -4.3F);
        this.SnakeHeadL7.addBox(-1.0F, -1.3F, -2.5F, 2, 1, 3, 0.0F);
        this.setRotateAngle(this.SnakeHeadL7, 0.8813912722571364F, 0.4293509959906051F, -0.8361872446304832F);
        this.SnakeJawL6 = new AdvancedModelBox(this, 15, 37);
        this.SnakeJawL6.setPos(0.0F, 0.31F, -0.3F);
        this.SnakeJawL6.addBox(-1.0F, -0.3F, -2.2F, 2, 1, 3, 0.0F);
        this.SnakeBodyL5 = new AdvancedModelBox(this, 43, 52);
        this.SnakeBodyL5.setPos(0.0F, 0.4F, -5.6F);
        this.SnakeBodyL5.addBox(-0.5F, -1.0F, -5.0F, 1, 1, 6, 0.0F);
        this.setRotateAngle(this.SnakeBodyL5, 0.6981317007977318F, 1.0471975511965976F, 0.2792526803190927F);
        this.SnakeBaseL5 = new AdvancedModelBox(this, 25, 50);
        this.SnakeBaseL5.setPos(1.0F, -4.5F, 3.3F);
        this.SnakeBaseL5.addBox(-0.5F, -1.0F, -5.8F, 1, 1, 6, 0.0F);
        this.setRotateAngle(this.SnakeBaseL5, -2.199463923363254F, 0.15812683023068624F, 2.255838058202671F);
        this.SnakeFang2R1 = new AdvancedModelBox(this, 31, 37);
        this.SnakeFang2R1.setPos(1.75F, -0.09F, -0.2F);
        this.SnakeFang2R1.addBox(-1.0F, -0.3F, -2.2F, 0, 1, 1, 0.0F);
        this.SnakeJawL2 = new AdvancedModelBox(this, 15, 37);
        this.SnakeJawL2.setPos(0.0F, 0.31F, -0.3F);
        this.SnakeJawL2.addBox(-1.0F, -0.3F, -2.2F, 2, 1, 3, 0.0F);
        this.SnakeJawL7 = new AdvancedModelBox(this, 15, 37);
        this.SnakeJawL7.setPos(0.0F, 0.31F, -0.3F);
        this.SnakeJawL7.addBox(-1.0F, -0.3F, -2.2F, 2, 1, 3, 0.0F);
        this.SnakeFang2R4 = new AdvancedModelBox(this, 31, 37);
        this.SnakeFang2R4.setPos(1.75F, -0.09F, -0.2F);
        this.SnakeFang2R4.addBox(-1.0F, -0.3F, -2.2F, 0, 1, 1, 0.0F);
        this.SnakeFang2R2 = new AdvancedModelBox(this, 31, 37);
        this.SnakeFang2R2.setPos(1.75F, -0.09F, -0.2F);
        this.SnakeFang2R2.addBox(-1.0F, -0.3F, -2.2F, 0, 1, 1, 0.0F);
        this.SnakeFang2R3 = new AdvancedModelBox(this, 31, 37);
        this.SnakeFang2R3.setPos(1.75F, -0.09F, -0.2F);
        this.SnakeFang2R3.addBox(-1.0F, -0.3F, -2.2F, 0, 1, 1, 0.0F);
        this.Head = new AdvancedModelBox(this, 0, 0);
        this.Head.setPos(0.0F, -12.0F, 0.0F);
        this.Head.addBox(-4.0F, -8.0F, -4.0F, 8, 8, 8, 0.0F);
        this.setRotateAngle(this.Head, 0.06771877497737998F, 0.0F, 0.0F);
        this.SnakeFang2L2 = new AdvancedModelBox(this, 31, 37);
        this.SnakeFang2L2.setPos(1.75F, -0.09F, -0.2F);
        this.SnakeFang2L2.addBox(-1.0F, -0.3F, -2.2F, 0, 1, 1, 0.0F);
        this.SnakeBodyR6 = new AdvancedModelBox(this, 43, 52);
        this.SnakeBodyR6.setPos(0.0F, 0.4F, -5.6F);
        this.SnakeBodyR6.addBox(-0.5F, -1.0F, -5.0F, 1, 1, 6, 0.0F);
        this.setRotateAngle(this.SnakeBodyR6, 1.3786355761503208F, -1.0170033551370958F, -0.6553711341238707F);
        this.SnakeBodyL2 = new AdvancedModelBox(this, 43, 52);
        this.SnakeBodyL2.setPos(0.0F, 0.4F, -5.6F);
        this.SnakeBodyL2.addBox(-0.5F, -1.0F, -5.0F, 1, 1, 6, 0.0F);
        this.setRotateAngle(this.SnakeBodyL2, 0.8651597102135892F, 2.4586453172844123F, 0.0F);
        this.SnakeHeadR1 = new AdvancedModelBox(this, 6, 36);
        this.SnakeHeadR1.setPos(0.0F, 0.1F, -4.3F);
        this.SnakeHeadR1.addBox(-1.0F, -1.3F, -2.5F, 2, 1, 3, 0.0F);
        this.setRotateAngle(this.SnakeHeadR1, -0.11292280260403312F, -0.06771877497737998F, 0.2033308578573394F);
        this.SnakeFang2L6 = new AdvancedModelBox(this, 31, 37);
        this.SnakeFang2L6.setPos(1.75F, -0.09F, -0.2F);
        this.SnakeFang2L6.addBox(-1.0F, -0.3F, -2.2F, 0, 1, 1, 0.0F);
        this.SnakeBaseR6 = new AdvancedModelBox(this, 25, 50);
        this.SnakeBaseR6.setPos(-3.0F, -6.5F, 2.3F);
        this.SnakeBaseR6.addBox(-0.5F, -1.0F, -5.8F, 1, 1, 6, 0.0F);
        this.setRotateAngle(this.SnakeBaseR6, -1.7976891295541593F, 0.0F, -1.9896753472735356F);
        this.SnakeBodyR7 = new AdvancedModelBox(this, 43, 52);
        this.SnakeBodyR7.setPos(0.0F, 0.4F, -5.6F);
        this.SnakeBodyR7.addBox(-0.5F, -1.0F, -5.0F, 1, 1, 6, 0.0F);
        this.setRotateAngle(this.SnakeBodyR7, 0.8726646259971648F, -1.4690436314036273F, 0.0F);
        this.SnakeFang1L3 = new AdvancedModelBox(this, 31, 37);
        this.SnakeFang1L3.setPos(0.25F, -0.09F, -0.2F);
        this.SnakeFang1L3.addBox(-1.0F, -0.3F, -2.2F, 0, 1, 1, 0.0F);
        this.SnakeFang1L5 = new AdvancedModelBox(this, 31, 37);
        this.SnakeFang1L5.setPos(0.25F, -0.09F, -0.2F);
        this.SnakeFang1L5.addBox(-1.0F, -0.3F, -2.2F, 0, 1, 1, 0.0F);
        this.SnakeHeadR4 = new AdvancedModelBox(this, 6, 36);
        this.SnakeHeadR4.setPos(0.0F, 0.1F, -4.3F);
        this.SnakeHeadR4.addBox(-1.0F, -1.3F, -2.5F, 2, 1, 3, 0.0F);
        this.setRotateAngle(this.SnakeHeadR4, 0.5197590512439113F, 0.0F, 0.2792526803190927F);
        this.SnakeFang2L1 = new AdvancedModelBox(this, 31, 37);
        this.SnakeFang2L1.setPos(1.75F, -0.09F, -0.2F);
        this.SnakeFang2L1.addBox(-1.0F, -0.3F, -2.2F, 0, 1, 1, 0.0F);
        this.SnakeJawL1 = new AdvancedModelBox(this, 15, 37);
        this.SnakeJawL1.setPos(0.0F, 0.31F, -0.3F);
        this.SnakeJawL1.addBox(-1.0F, -0.3F, -2.2F, 2, 1, 3, 0.0F);
        this.SnakeFang1L6 = new AdvancedModelBox(this, 31, 37);
        this.SnakeFang1L6.setPos(0.25F, -0.09F, -0.2F);
        this.SnakeFang1L6.addBox(-1.0F, -0.3F, -2.2F, 0, 1, 1, 0.0F);
        this.SnakeBodyL7 = new AdvancedModelBox(this, 43, 52);
        this.SnakeBodyL7.setPos(0.0F, 0.4F, -5.6F);
        this.SnakeBodyL7.addBox(-0.5F, -1.0F, -5.0F, 1, 1, 6, 0.0F);
        this.setRotateAngle(this.SnakeBodyL7, 0.8726646259971648F, 1.4690436314036273F, 0.0F);
        this.SnakeBodyR1 = new AdvancedModelBox(this, 43, 52);
        this.SnakeBodyR1.setPos(0.0F, 0.4F, -5.6F);
        this.SnakeBodyR1.addBox(-0.5F, -1.0F, -5.0F, 1, 1, 6, 0.0F);
        this.setRotateAngle(this.SnakeBodyR1, 1.6498597419102397F, 0.0F, 0.0F);
        this.SnakeJawR7 = new AdvancedModelBox(this, 15, 37);
        this.SnakeJawR7.setPos(0.0F, 0.11F, -0.3F);
        this.SnakeJawR7.addBox(-1.0F, -0.3F, -2.2F, 2, 1, 3, 0.0F);
        this.SnakeJawR6 = new AdvancedModelBox(this, 15, 37);
        this.SnakeJawR6.setPos(0.0F, 0.11F, -0.3F);
        this.SnakeJawR6.addBox(-1.0F, -0.3F, -2.2F, 2, 1, 3, 0.0F);
        this.SnakeBaseL3 = new AdvancedModelBox(this, 25, 50);
        this.SnakeBaseL3.setPos(3.0F, -6.5F, 1.3F);
        this.SnakeBaseL3.addBox(-0.5F, -1.0F, -5.8F, 1, 1, 6, 0.0F);
        this.setRotateAngle(this.SnakeBaseL3, -1.7966419320029627F, 0.022514747350726852F, 0.92031211457661F);
        this.SnakeFang1R5 = new AdvancedModelBox(this, 31, 37);
        this.SnakeFang1R5.setPos(0.25F, -0.09F, -0.2F);
        this.SnakeFang1R5.addBox(-1.0F, -0.3F, -2.2F, 0, 1, 1, 0.0F);
        this.SnakeFang2L7 = new AdvancedModelBox(this, 31, 37);
        this.SnakeFang2L7.setPos(1.75F, -0.09F, -0.2F);
        this.SnakeFang2L7.addBox(-1.0F, -0.3F, -2.2F, 0, 1, 1, 0.0F);
        this.SnakeJawR2 = new AdvancedModelBox(this, 15, 37);
        this.SnakeJawR2.setPos(0.0F, 0.11F, -0.3F);
        this.SnakeJawR2.addBox(-1.0F, -0.3F, -2.2F, 2, 1, 3, 0.0F);
        this.SnakeHeadL1 = new AdvancedModelBox(this, 6, 36);
        this.SnakeHeadL1.setPos(0.0F, 0.1F, -4.3F);
        this.SnakeHeadL1.addBox(-1.0F, -1.3F, -2.5F, 2, 1, 3, 0.0F);
        this.setRotateAngle(this.SnakeHeadL1, -0.11292280260403312F, 0.06771877497737998F, -0.2033308578573394F);
        this.SnakeFang1L7 = new AdvancedModelBox(this, 31, 37);
        this.SnakeFang1L7.setPos(0.25F, -0.09F, -0.2F);
        this.SnakeFang1L7.addBox(-1.0F, -0.3F, -2.2F, 0, 1, 1, 0.0F);
        this.SnakeBaseR1 = new AdvancedModelBox(this, 25, 50);
        this.SnakeBaseR1.setPos(-3.0F, -5.5F, -2.7F);
        this.SnakeBaseR1.addBox(-0.5F, -1.0F, -5.8F, 1, 1, 6, 0.0F);
        this.setRotateAngle(this.SnakeBaseR1, -1.6046557142835864F, 0.0F, -0.22689280275926282F);
        this.SnakeJawR5 = new AdvancedModelBox(this, 15, 37);
        this.SnakeJawR5.setPos(0.0F, 0.11F, -0.3F);
        this.SnakeJawR5.addBox(-1.0F, -0.3F, -2.2F, 2, 1, 3, 0.0F);
        this.SnakeHeadL2 = new AdvancedModelBox(this, 6, 36);
        this.SnakeHeadL2.setPos(0.0F, 0.1F, -4.3F);
        this.SnakeHeadL2.addBox(-1.0F, -1.3F, -2.5F, 2, 1, 3, 0.0F);
        this.setRotateAngle(this.SnakeHeadL2, 0.7749261878854823F, -0.0F, 0.17453292519943295F);
        this.SnakeBaseR4 = new AdvancedModelBox(this, 25, 50);
        this.SnakeBaseR4.setPos(-1.0F, -6.5F, 3.3F);
        this.SnakeBaseR4.addBox(-0.5F, -1.0F, -5.8F, 1, 1, 6, 0.0F);
        this.setRotateAngle(this.SnakeBaseR4, -2.1282544898818854F, 0.0F, -0.2617993877991494F);
        this.SnakeFang1L1 = new AdvancedModelBox(this, 31, 37);
        this.SnakeFang1L1.setPos(0.25F, -0.09F, -0.2F);
        this.SnakeFang1L1.addBox(-1.0F, -0.3F, -2.2F, 0, 1, 1, 0.0F);
        this.SnakeJawR4 = new AdvancedModelBox(this, 15, 37);
        this.SnakeJawR4.setPos(0.0F, 0.11F, -0.3F);
        this.SnakeJawR4.addBox(-1.0F, -0.3F, -2.2F, 2, 1, 3, 0.0F);
        this.SnakeFang1L2 = new AdvancedModelBox(this, 31, 37);
        this.SnakeFang1L2.setPos(0.25F, -0.09F, -0.2F);
        this.SnakeFang1L2.addBox(-1.0F, -0.3F, -2.2F, 0, 1, 1, 0.0F);
        this.SnakeBodyL1 = new AdvancedModelBox(this, 43, 52);
        this.SnakeBodyL1.setPos(0.0F, 0.4F, -5.6F);
        this.SnakeBodyL1.addBox(-0.5F, -1.0F, -5.0F, 1, 1, 6, 0.0F);
        this.setRotateAngle(this.SnakeBodyL1, 1.6498597419102397F, 0.0F, 0.0F);
        this.SnakeFang2L5 = new AdvancedModelBox(this, 31, 37);
        this.SnakeFang2L5.setPos(1.75F, -0.09F, -0.2F);
        this.SnakeFang2L5.addBox(-1.0F, -0.3F, -2.2F, 0, 1, 1, 0.0F);
        this.SnakeJawL3 = new AdvancedModelBox(this, 15, 37);
        this.SnakeJawL3.setPos(0.0F, 0.31F, -0.3F);
        this.SnakeJawL3.addBox(-1.0F, -0.3F, -2.2F, 2, 1, 3, 0.0F);
        this.SnakeBaseL1 = new AdvancedModelBox(this, 25, 50);
        this.SnakeBaseL1.setPos(3.0F, -5.5F, -2.7F);
        this.SnakeBaseL1.addBox(-0.5F, -1.0F, -5.8F, 1, 1, 6, 0.0F);
        this.setRotateAngle(this.SnakeBaseL1, -1.6046557142835864F, 0.0F, 0.22689280275926282F);
        this.SnakeBaseL6 = new AdvancedModelBox(this, 25, 50);
        this.SnakeBaseL6.setPos(3.0F, -6.5F, 2.3F);
        this.SnakeBaseL6.addBox(-0.5F, -1.0F, -5.8F, 1, 1, 6, 0.0F);
        this.setRotateAngle(this.SnakeBaseL6, -1.7976891295541593F, 0.0F, 1.9896753472735356F);
        this.SnakeBodyR4 = new AdvancedModelBox(this, 43, 52);
        this.SnakeBodyR4.setPos(0.0F, 0.4F, -5.6F);
        this.SnakeBodyR4.addBox(-0.5F, -1.0F, -5.0F, 1, 1, 6, 0.0F);
        this.setRotateAngle(this.SnakeBodyR4, 1.5159929882822747F, 0.0F, 0.0F);
        this.SnakeHeadL3 = new AdvancedModelBox(this, 6, 36);
        this.SnakeHeadL3.setPos(0.0F, 0.1F, -4.3F);
        this.SnakeHeadL3.addBox(-1.0F, -1.3F, -2.5F, 2, 1, 3, 0.0F);
        this.setRotateAngle(this.SnakeHeadL3, 0.6942919764433443F, 0.06981317007977318F, 0.15707963267948966F);
        this.SnakeBaseR3 = new AdvancedModelBox(this, 25, 50);
        this.SnakeBaseR3.setPos(-3.0F, -6.5F, 1.3F);
        this.SnakeBaseR3.addBox(-0.5F, -1.0F, -5.8F, 1, 1, 6, 0.0F);
        this.setRotateAngle(this.SnakeBaseR3, -1.7966419320029627F, -0.022514747350726852F, -0.92031211457661F);
        this.SnakeHeadR4_1 = new AdvancedModelBox(this, 6, 36);
        this.SnakeHeadR4_1.setPos(0.0F, 0.1F, -4.3F);
        this.SnakeHeadR4_1.addBox(-1.0F, -1.3F, -2.5F, 2, 1, 3, 0.0F);
        this.setRotateAngle(this.SnakeHeadR4_1, 0.5197590512439113F, 0.0F, -0.2792526803190927F);
        this.SnakeBaseR7 = new AdvancedModelBox(this, 25, 50);
        this.SnakeBaseR7.setPos(-4.0F, -3.5F, 1.3F);
        this.SnakeBaseR7.addBox(-0.5F, -1.0F, -5.8F, 1, 1, 6, 0.0F);
        this.setRotateAngle(this.SnakeBaseR7, -1.5707963267948966F, 0.0F, -2.2734658836478134F);
        this.SnakeFang1R6 = new AdvancedModelBox(this, 31, 37);
        this.SnakeFang1R6.setPos(0.25F, -0.09F, -0.2F);
        this.SnakeFang1R6.addBox(-1.0F, -0.3F, -2.2F, 0, 1, 1, 0.0F);
        this.SnakeBodyL3 = new AdvancedModelBox(this, 43, 52);
        this.SnakeBodyL3.setPos(0.0F, 0.4F, -5.6F);
        this.SnakeBodyL3.addBox(-0.5F, -1.0F, -5.0F, 1, 1, 6, 0.0F);
        this.setRotateAngle(this.SnakeBodyL3, 1.0622073827637488F, 0.7457791893771769F, 0.0F);
        this.SnakeBodyR2 = new AdvancedModelBox(this, 43, 52);
        this.SnakeBodyR2.setPos(0.0F, 0.4F, -5.6F);
        this.SnakeBodyR2.addBox(-0.5F, -1.0F, -5.0F, 1, 1, 6, 0.0F);
        this.setRotateAngle(this.SnakeBodyR2, 0.8651597102135892F, -2.4586453172844123F, 0.0F);
        this.SnakeFang1R4 = new AdvancedModelBox(this, 31, 37);
        this.SnakeFang1R4.setPos(0.25F, -0.09F, -0.2F);
        this.SnakeFang1R4.addBox(-1.0F, -0.3F, -2.2F, 0, 1, 1, 0.0F);
        this.SnakeFang2L3 = new AdvancedModelBox(this, 31, 37);
        this.SnakeFang2L3.setPos(1.75F, -0.09F, -0.2F);
        this.SnakeFang2L3.addBox(-1.0F, -0.3F, -2.2F, 0, 1, 1, 0.0F);
        this.SnakeBodyR5 = new AdvancedModelBox(this, 43, 52);
        this.SnakeBodyR5.setPos(0.0F, 0.4F, -5.6F);
        this.SnakeBodyR5.addBox(-0.5F, -1.0F, -5.0F, 1, 1, 6, 0.0F);
        this.setRotateAngle(this.SnakeBodyR5, 0.6981317007977318F, -1.0471975511965976F, -0.2792526803190927F);
        this.SnakeHeadR6 = new AdvancedModelBox(this, 6, 36);
        this.SnakeHeadR6.setPos(0.0F, 0.1F, -4.3F);
        this.SnakeHeadR6.addBox(-1.0F, -1.3F, -2.5F, 2, 1, 3, 0.0F);
        this.setRotateAngle(this.SnakeHeadR6, 0.767944870877505F, -0.6108652381980153F, 0.8726646259971648F);
        this.SnakeBodyL4 = new AdvancedModelBox(this, 43, 52);
        this.SnakeBodyL4.setPos(0.0F, 0.4F, -5.6F);
        this.SnakeBodyL4.addBox(-0.5F, -1.0F, -5.0F, 1, 1, 6, 0.0F);
        this.setRotateAngle(this.SnakeBodyL4, 1.5159929882822747F, 0.0F, 0.0F);
        this.Head_Details = new AdvancedModelBox(this, 32, 0);
        this.Head_Details.setPos(0.0F, 0.0F, 0.0F);
        this.Head_Details.addBox(-4.0F, -8.0F, -4.0F, 8, 8, 8, 0.0F);
        this.snakeBodyL6 = new AdvancedModelBox(this, 43, 52);
        this.snakeBodyL6.setPos(0.0F, 0.4F, -5.6F);
        this.snakeBodyL6.addBox(-0.5F, -1.0F, -5.0F, 1, 1, 6, 0.0F);
        this.setRotateAngle(this.snakeBodyL6, 1.3786355761503208F, 1.0170033551370958F, 0.6553711341238707F);
        this.SnakeFang2R5 = new AdvancedModelBox(this, 31, 37);
        this.SnakeFang2R5.setPos(1.75F, -0.09F, -0.2F);
        this.SnakeFang2R5.addBox(-1.0F, -0.3F, -2.2F, 0, 1, 1, 0.0F);
        this.SnakeJawR4_1 = new AdvancedModelBox(this, 15, 37);
        this.SnakeJawR4_1.setPos(0.0F, 0.31F, -0.3F);
        this.SnakeJawR4_1.addBox(-1.0F, -0.3F, -2.2F, 2, 1, 3, 0.0F);
        this.SnakeJawL5 = new AdvancedModelBox(this, 15, 37);
        this.SnakeJawL5.setPos(0.0F, 0.31F, -0.3F);
        this.SnakeJawL5.addBox(-1.0F, -0.3F, -2.2F, 2, 1, 3, 0.0F);
        this.SnakeBaseL7 = new AdvancedModelBox(this, 25, 50);
        this.SnakeBaseL7.setPos(4.0F, -3.5F, 1.3F);
        this.SnakeBaseL7.addBox(-0.5F, -1.0F, -5.8F, 1, 1, 6, 0.0F);
        this.setRotateAngle(this.SnakeBaseL7, -1.5707963267948966F, 0.0F, 2.2734658836478134F);
        this.SnakeFang1R2 = new AdvancedModelBox(this, 31, 37);
        this.SnakeFang1R2.setPos(0.25F, -0.09F, -0.2F);
        this.SnakeFang1R2.addBox(-1.0F, -0.3F, -2.2F, 0, 1, 1, 0.0F);
        this.SnakeHeadR3 = new AdvancedModelBox(this, 6, 36);
        this.SnakeHeadR3.setPos(0.0F, 0.1F, -4.3F);
        this.SnakeHeadR3.addBox(-1.0F, -1.3F, -2.5F, 2, 1, 3, 0.0F);
        this.setRotateAngle(this.SnakeHeadR3, 0.6942919764433443F, -0.06981317007977318F, 0.15707963267948966F);
        this.SnakeHeadR1.addChild(this.SnakeJawR1);
        this.SnakeHeadR7.addChild(this.SnakeFang2R7);
        this.SnakeHeadR3.addChild(this.SnakeJawR3);
        this.SnakeBodyR5.addChild(this.SnakeHeadR5);
        this.SnakeHeadR4_1.addChild(this.SnakeFang1R4_1);
        this.SnakeBodyR2.addChild(this.SnakeHeadR2);
        this.SnakeHeadR6.addChild(this.SnakeFang2R6);
        this.Head_Details.addChild(this.SnakeBaseL4);
        this.Head_Details.addChild(this.SnakeBaseR5);
        this.Head_Details.addChild(this.SnakeBaseR2);
        this.SnakeHeadR7.addChild(this.SnakeFang1R7);
        this.SnakeBodyR7.addChild(this.SnakeHeadR7);
        this.SnakeHeadR4_1.addChild(this.SnakeFang2R4_1);
        this.Head_Details.addChild(this.SnakeBaseL2);
        this.SnakeHeadR3.addChild(this.SnakeFang1R3);
        this.snakeBodyL6.addChild(this.SnakeHeadL6);
        this.SnakeHeadR1.addChild(this.SnakeFang1R1);
        this.SnakeBodyL5.addChild(this.SnakeHeadL5);
        this.SnakeBaseR3.addChild(this.SnakeBodyR3);
        this.SnakeBodyL7.addChild(this.SnakeHeadL7);
        this.SnakeHeadL6.addChild(this.SnakeJawL6);
        this.SnakeBaseL5.addChild(this.SnakeBodyL5);
        this.Head_Details.addChild(this.SnakeBaseL5);
        this.SnakeHeadR1.addChild(this.SnakeFang2R1);
        this.SnakeHeadL2.addChild(this.SnakeJawL2);
        this.SnakeHeadL7.addChild(this.SnakeJawL7);
        this.SnakeHeadR4.addChild(this.SnakeFang2R4);
        this.SnakeHeadR2.addChild(this.SnakeFang2R2);
        this.SnakeHeadR3.addChild(this.SnakeFang2R3);
        this.SnakeHeadL2.addChild(this.SnakeFang2L2);
        this.SnakeBaseR6.addChild(this.SnakeBodyR6);
        this.SnakeBaseL2.addChild(this.SnakeBodyL2);
        this.SnakeBodyR1.addChild(this.SnakeHeadR1);
        this.SnakeHeadL6.addChild(this.SnakeFang2L6);
        this.Head_Details.addChild(this.SnakeBaseR6);
        this.SnakeBaseR7.addChild(this.SnakeBodyR7);
        this.SnakeHeadL3.addChild(this.SnakeFang1L3);
        this.SnakeHeadL5.addChild(this.SnakeFang1L5);
        this.SnakeBodyR4.addChild(this.SnakeHeadR4);
        this.SnakeHeadL1.addChild(this.SnakeFang2L1);
        this.SnakeHeadL1.addChild(this.SnakeJawL1);
        this.SnakeHeadL6.addChild(this.SnakeFang1L6);
        this.SnakeBaseL7.addChild(this.SnakeBodyL7);
        this.SnakeBaseR1.addChild(this.SnakeBodyR1);
        this.SnakeHeadR7.addChild(this.SnakeJawR7);
        this.SnakeHeadR6.addChild(this.SnakeJawR6);
        this.Head_Details.addChild(this.SnakeBaseL3);
        this.SnakeHeadR5.addChild(this.SnakeFang1R5);
        this.SnakeHeadL7.addChild(this.SnakeFang2L7);
        this.SnakeHeadR2.addChild(this.SnakeJawR2);
        this.SnakeBodyL1.addChild(this.SnakeHeadL1);
        this.SnakeHeadL7.addChild(this.SnakeFang1L7);
        this.Head_Details.addChild(this.SnakeBaseR1);
        this.SnakeHeadR5.addChild(this.SnakeJawR5);
        this.SnakeBodyL2.addChild(this.SnakeHeadL2);
        this.Head_Details.addChild(this.SnakeBaseR4);
        this.SnakeHeadL1.addChild(this.SnakeFang1L1);
        this.SnakeHeadR4.addChild(this.SnakeJawR4);
        this.SnakeHeadL2.addChild(this.SnakeFang1L2);
        this.SnakeBaseL1.addChild(this.SnakeBodyL1);
        this.SnakeHeadL5.addChild(this.SnakeFang2L5);
        this.SnakeHeadL3.addChild(this.SnakeJawL3);
        this.Head_Details.addChild(this.SnakeBaseL1);
        this.Head_Details.addChild(this.SnakeBaseL6);
        this.SnakeBaseR4.addChild(this.SnakeBodyR4);
        this.SnakeBodyL3.addChild(this.SnakeHeadL3);
        this.Head_Details.addChild(this.SnakeBaseR3);
        this.SnakeBodyL4.addChild(this.SnakeHeadR4_1);
        this.Head_Details.addChild(this.SnakeBaseR7);
        this.SnakeHeadR6.addChild(this.SnakeFang1R6);
        this.SnakeBaseL3.addChild(this.SnakeBodyL3);
        this.SnakeBaseR2.addChild(this.SnakeBodyR2);
        this.SnakeHeadR4.addChild(this.SnakeFang1R4);
        this.SnakeHeadL3.addChild(this.SnakeFang2L3);
        this.SnakeBaseR5.addChild(this.SnakeBodyR5);
        this.SnakeBodyR6.addChild(this.SnakeHeadR6);
        this.SnakeBaseL4.addChild(this.SnakeBodyL4);
        this.Head.addChild(this.Head_Details);
        this.SnakeBaseL6.addChild(this.snakeBodyL6);
        this.SnakeHeadR5.addChild(this.SnakeFang2R5);
        this.SnakeHeadR4_1.addChild(this.SnakeJawR4_1);
        this.SnakeHeadL5.addChild(this.SnakeJawL5);
        this.Head_Details.addChild(this.SnakeBaseL7);
        this.SnakeHeadR2.addChild(this.SnakeFang1R2);
        this.SnakeBodyR3.addChild(this.SnakeHeadR3);
    }

    @Override
    public void setAngles(Entity entity, float limbAngle, float limbDistance, float animationProgress, float headYaw, float headPitch) {
    }

    @Override
    public Iterable<BasicModelPart> parts() {
        return ImmutableList.of(this.Head);
    }


    @Override
    public Iterable<AdvancedModelBox> getAllParts() {
        return ImmutableList.of(this.Head);
    }
}
