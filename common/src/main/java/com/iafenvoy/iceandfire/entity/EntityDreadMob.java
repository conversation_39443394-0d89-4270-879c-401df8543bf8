package com.iafenvoy.iceandfire.entity;

import com.iafenvoy.iceandfire.entity.ai.DreadMinionMode;
import com.iafenvoy.iceandfire.entity.util.IDreadMob;
import com.iafenvoy.iceandfire.entity.util.IHumanoid;
import com.iafenvoy.iceandfire.registry.IafEntities;
import net.minecraft.enchantment.EnchantmentHelper;
import net.minecraft.entity.*;
import net.minecraft.entity.damage.DamageSource;
import net.minecraft.entity.data.DataTracker;
import net.minecraft.entity.data.TrackedData;
import net.minecraft.entity.data.TrackedDataHandlerRegistry;
import net.minecraft.entity.mob.AbstractSkeletonEntity;
import net.minecraft.entity.mob.HostileEntity;
import net.minecraft.entity.mob.ZombieEntity;
import net.minecraft.entity.passive.AbstractHorseEntity;
import net.minecraft.entity.passive.AnimalEntity;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.item.ItemStack;
import net.minecraft.nbt.NbtCompound;
import net.minecraft.registry.RegistryKeys;
import net.minecraft.registry.tag.EntityTypeTags;
import net.minecraft.util.Identifier;
import net.minecraft.server.ServerConfigHandler;
import net.minecraft.world.ServerWorldAccess;
import net.minecraft.world.World;

import java.util.Optional;
import java.util.UUID;

public class EntityDreadMob extends HostileEntity implements IDreadMob {
    protected static final TrackedData<Optional<UUID>> COMMANDER_UNIQUE_ID = DataTracker.registerData(EntityDreadMob.class, TrackedDataHandlerRegistry.OPTIONAL_UUID);
    protected static final TrackedData<String> MINION_MODE = DataTracker.registerData(EntityDreadMob.class, TrackedDataHandlerRegistry.STRING);
    protected static final TrackedData<Integer> SUMMONED_LIFETIME = DataTracker.registerData(EntityDreadMob.class, TrackedDataHandlerRegistry.INTEGER);
    protected static final TrackedData<Optional<UUID>> SUMMONER_LICH_ID = DataTracker.registerData(EntityDreadMob.class, TrackedDataHandlerRegistry.OPTIONAL_UUID);

    private int summonedTicks = 0;

    public EntityDreadMob(EntityType<? extends HostileEntity> t, World worldIn) {
        super(t, worldIn);
    }

    public static Entity necromancyEntity(LivingEntity entity) {
        if (entity.getType().isIn(EntityTypeTags.ARTHROPOD)) {
            EntityDreadScuttler lichSummoned = new EntityDreadScuttler(IafEntities.DREAD_SCUTTLER.get(), entity.getWorld());
            float readInScale = (entity.getWidth() / 1.5F);
            if (entity.getWorld() instanceof ServerWorldAccess serverWorldAccess)
                lichSummoned.initialize(serverWorldAccess, entity.getWorld().getLocalDifficulty(entity.getBlockPos()), SpawnReason.MOB_SUMMONED, null);
            lichSummoned.setSize(readInScale);
            return lichSummoned;
        }
        if (entity instanceof ZombieEntity || entity instanceof IHumanoid) {
            EntityDreadGhoul lichSummoned = new EntityDreadGhoul(IafEntities.DREAD_GHOUL.get(), entity.getWorld());
            float readInScale = (entity.getWidth() / 0.6F);
            if (entity.getWorld() instanceof ServerWorldAccess serverWorldAccess)
                lichSummoned.initialize(serverWorldAccess, entity.getWorld().getLocalDifficulty(entity.getBlockPos()), SpawnReason.MOB_SUMMONED, null);
            lichSummoned.setSize(readInScale);
            return lichSummoned;
        }
        if (entity.getType().isIn(EntityTypeTags.UNDEAD) || entity instanceof AbstractSkeletonEntity || entity instanceof PlayerEntity) {
            EntityDreadThrall lichSummoned = new EntityDreadThrall(IafEntities.DREAD_THRALL.get(), entity.getWorld());
            if (entity.getWorld() instanceof ServerWorldAccess serverWorldAccess) {
                lichSummoned.initialize(serverWorldAccess, entity.getWorld().getLocalDifficulty(entity.getBlockPos()), SpawnReason.MOB_SUMMONED, null);
            }
            lichSummoned.setCustomArmorHead(false);
            lichSummoned.setCustomArmorChest(false);
            lichSummoned.setCustomArmorLegs(false);
            lichSummoned.setCustomArmorFeet(false);
            for (EquipmentSlot slot : EquipmentSlot.values())
                lichSummoned.equipStack(slot, entity.getEquippedStack(slot));
            return lichSummoned;
        }
        if (entity instanceof AbstractHorseEntity)
            return new EntityDreadHorse(IafEntities.DREAD_HORSE.get(), entity.getWorld());
        if (entity instanceof AnimalEntity) {
            EntityDreadBeast lichSummoned = new EntityDreadBeast(IafEntities.DREAD_BEAST.get(), entity.getWorld());
            float readInScale = (entity.getWidth() / 1.2F);
            if (entity.getWorld() instanceof ServerWorldAccess serverWorldAccess)
                lichSummoned.initialize(serverWorldAccess, entity.getWorld().getLocalDifficulty(entity.getBlockPos()), SpawnReason.MOB_SUMMONED, null);
            lichSummoned.setSize(readInScale);
            return lichSummoned;
        }
        return null;
    }

    @Override
    protected void initDataTracker(DataTracker.Builder builder) {
        super.initDataTracker(builder);
        builder.add(COMMANDER_UNIQUE_ID, Optional.empty());
        builder.add(MINION_MODE, DreadMinionMode.WANDER.asString());
        builder.add(SUMMONED_LIFETIME, 0);
        builder.add(SUMMONER_LICH_ID, Optional.empty());
    }

    @Override
    public void writeCustomDataToNbt(NbtCompound compound) {
        super.writeCustomDataToNbt(compound);
        if (this.getCommanderId() != null) {
            compound.putUuid("CommanderUUID", this.getCommanderId());
        }
        compound.putString("MinionMode", this.getMinionMode().asString());
        compound.putInt("SummonedLifetime", this.getSummonedLifetime());
        compound.putInt("SummonedTicks", this.summonedTicks);
    }

    @Override
    public void readCustomDataFromNbt(NbtCompound compound) {
        super.readCustomDataFromNbt(compound);
        UUID uuid;
        if (compound.containsUuid("CommanderUUID")) {
            uuid = compound.getUuid("CommanderUUID");
        } else {
            String s = compound.getString("CommanderUUID");
            uuid = ServerConfigHandler.getPlayerUuidByName(this.getServer(), s);
        }

        if (uuid != null) {
            try {
                this.setCommanderId(uuid);
            } catch (Throwable ignored) {
            }
        }

        if (compound.contains("MinionMode")) {
            this.setMinionMode(DreadMinionMode.fromString(compound.getString("MinionMode")));
        }

        if (compound.contains("SummonedLifetime")) {
            this.setSummonedLifetime(compound.getInt("SummonedLifetime"));
        }

        if (compound.contains("SummonedTicks")) {
            this.summonedTicks = compound.getInt("SummonedTicks");
        }
    }


    @Override
    public boolean isTeammate(Entity entityIn) {
        return entityIn instanceof IDreadMob || super.isTeammate(entityIn);
    }

    public UUID getCommanderId() {
        return this.dataTracker.get(COMMANDER_UNIQUE_ID).orElse(null);
    }

    public void setCommanderId(UUID uuid) {
        this.dataTracker.set(COMMANDER_UNIQUE_ID, Optional.ofNullable(uuid));
    }

    public DreadMinionMode getMinionMode() {
        return DreadMinionMode.fromString(this.dataTracker.get(MINION_MODE));
    }

    public void setMinionMode(DreadMinionMode mode) {
        this.dataTracker.set(MINION_MODE, mode.asString());
    }

    public void setSummonedLifetime(int ticks) {
        this.dataTracker.set(SUMMONED_LIFETIME, ticks);
    }

    public int getSummonedLifetime() {
        return this.dataTracker.get(SUMMONED_LIFETIME);
    }

    public void setSummonerLichId(UUID lichId) {
        this.dataTracker.set(SUMMONER_LICH_ID, Optional.ofNullable(lichId));
    }

    public UUID getSummonerLichId() {
        return this.dataTracker.get(SUMMONER_LICH_ID).orElse(null);
    }

    @Override
    public void tick() {
        super.tick();

        // 移除召唤生物的生存时间限制，让它们永久存在
        // 召唤生物现在不会自然消失
    }

    @Override
    public boolean canTarget(LivingEntity target) {
        // 检查目标是否是戴有异界统御附魔的玩家
        if (target instanceof PlayerEntity player && hasOtherworldDominion(player)) {
            System.out.println("EntityDreadMob拒绝攻击戴有异界统御附魔的玩家: " + player.getName().getString());
            return false;
        }

        // 如果是召唤生物，不攻击召唤者驯服的生物
        if (this.getCommanderId() != null) {
            if (isPlayerTamedEntity(target, this.getCommanderId())) {
                System.out.println("EntityDreadMob拒绝攻击召唤者驯服的生物: " + target.getClass().getSimpleName());
                return false;
            }
        }

        // 检查是否攻击同一召唤者的其他召唤生物（无论自己是否为召唤生物）
        if (target instanceof EntityDreadMob otherDread) {
            // 如果双方都有召唤者且是同一个召唤者，不允许攻击
            if (this.getCommanderId() != null && otherDread.getCommanderId() != null &&
                this.getCommanderId().equals(otherDread.getCommanderId())) {
                System.out.println("拒绝攻击同队EntityDreadMob: " + otherDread.getClass().getSimpleName());
                return false;
            }
        }

        if (target instanceof com.iafenvoy.iceandfire.entity.EntityDreadKnight otherKnight) {
            // 如果双方都有召唤者且是同一个召唤者，不允许攻击
            if (this.getCommanderId() != null && otherKnight.getCommanderId() != null &&
                this.getCommanderId().equals(otherKnight.getCommanderId())) {
                System.out.println("拒绝攻击同队EntityDreadKnight: " + otherKnight.getClass().getSimpleName());
                return false;
            }
        }

        if (target instanceof com.iafenvoy.iceandfire.entity.EntityDreadHorse otherHorse) {
            // 如果双方都有召唤者且是同一个召唤者，不允许攻击
            if (this.getCommanderId() != null && otherHorse.getCommanderId() != null &&
                this.getCommanderId().equals(otherHorse.getCommanderId())) {
                System.out.println("拒绝攻击同队EntityDreadHorse: " + otherHorse.getClass().getSimpleName());
                return false;
            }

            // 检查是否有同队的惊悚尸骑在骑乘这匹马
            if (this.getCommanderId() != null && otherHorse.hasPassengers()) {
                for (Entity passenger : otherHorse.getPassengerList()) {
                    if (passenger instanceof com.iafenvoy.iceandfire.entity.EntityDreadKnight knight) {
                        if (knight.getCommanderId() != null &&
                            this.getCommanderId().equals(knight.getCommanderId())) {
                            System.out.println("拒绝攻击同队尸骑骑乘的战马: " + otherHorse.getClass().getSimpleName());
                            return false;
                        }
                    }
                }
            }
        }

        // 如果是召唤生物，应用额外的保护机制
        if (this.getCommanderId() != null) {
            System.out.println("EntityDreadMob canTarget检查: " + target.getClass().getSimpleName() +
                ", 召唤者ID: " + this.getCommanderId());

            // 跟随模式下不攻击任何生物
            if (this.getMinionMode() == DreadMinionMode.FOLLOW) {
                System.out.println("跟随模式，拒绝攻击任何目标");
                return false;
            }

            // 不攻击召唤者
            if (target instanceof PlayerEntity player) {
                if (player.getUuid().equals(this.getCommanderId())) {
                    System.out.println("拒绝攻击召唤者: " + player.getName().getString());
                    return false;
                }
            }

            System.out.println("允许攻击目标: " + target.getClass().getSimpleName());
        }

        return super.canTarget(target);
    }

    private boolean hasOtherworldDominion(PlayerEntity player) {
        ItemStack helmet = player.getEquippedStack(EquipmentSlot.HEAD);
        if (helmet.isEmpty()) return false;

        try {
            var enchantmentRegistry = player.getRegistryManager().get(RegistryKeys.ENCHANTMENT);
            var enchantmentEntry = enchantmentRegistry.getEntry(Identifier.of("iceandfire", "otherworld_dominion"));
            if (enchantmentEntry.isEmpty()) return false;

            return EnchantmentHelper.getLevel(enchantmentEntry.get(), helmet) > 0;
        } catch (Exception e) {
            return false;
        }
    }

    private boolean isPlayerTamedEntity(LivingEntity entity, UUID playerUuid) {
        // 检查是否是可驯服的实体且被指定玩家驯服
        if (entity instanceof net.minecraft.entity.passive.TameableEntity tameable) {
            return tameable.isTamed() && playerUuid.equals(tameable.getOwnerUuid());
        }

        // 检查是否是龙且被指定玩家驯服
        if (entity instanceof com.iafenvoy.iceandfire.entity.EntityDragonBase dragon) {
            return dragon.isTamed() && playerUuid.equals(dragon.getOwnerUuid());
        }

        return false;
    }

    @Override
    public void setAttacker(LivingEntity attacker) {
        // 如果攻击者是召唤者，忽略这次攻击，不设置为攻击者
        if (this.getCommanderId() != null && attacker instanceof PlayerEntity player) {
            if (player.getUuid().equals(this.getCommanderId())) {
                return; // 不设置召唤者为攻击者
            }
        }
        super.setAttacker(attacker);
    }

    @Override
    public void setTarget(LivingEntity target) {
        // 如果是召唤生物，应用保护机制
        if (this.getCommanderId() != null && target != null) {
            System.out.println("EntityDreadMob setTarget尝试: " + target.getClass().getSimpleName());

            // 跟随模式下不设置任何目标
            if (this.getMinionMode() == DreadMinionMode.FOLLOW) {
                System.out.println("跟随模式，拒绝设置任何目标");
                super.setTarget(null); // 强制清除目标
                return;
            }

            // 不设置召唤者为目标
            if (target instanceof PlayerEntity player) {
                if (player.getUuid().equals(this.getCommanderId())) {
                    System.out.println("拒绝设置召唤者为目标: " + player.getName().getString());
                    return;
                }
            }

            // 不设置同队召唤生物为目标
            if (target instanceof EntityDreadMob otherDread) {
                if (otherDread.getCommanderId() != null &&
                    this.getCommanderId().equals(otherDread.getCommanderId())) {
                    System.out.println("拒绝设置同队EntityDreadMob为目标");
                    return;
                }
            }
            if (target instanceof com.iafenvoy.iceandfire.entity.EntityDreadKnight otherKnight) {
                if (otherKnight.getCommanderId() != null &&
                    this.getCommanderId().equals(otherKnight.getCommanderId())) {
                    System.out.println("拒绝设置同队EntityDreadKnight为目标");
                    return;
                }
            }

            System.out.println("允许设置目标: " + target.getClass().getSimpleName());
        }
        super.setTarget(target);
    }

    @Override
    public boolean damage(DamageSource source, float amount) {
        // 如果伤害来源是召唤者，不产生敌意
        if (this.getCommanderId() != null && source.getAttacker() instanceof PlayerEntity player) {
            if (player.getUuid().equals(this.getCommanderId())) {
                // 受到伤害但不设置攻击者，避免产生敌意
                LivingEntity originalAttacker = this.getAttacker();
                boolean result = super.damage(source, amount);
                this.setAttacker(originalAttacker); // 恢复原来的攻击者
                return result;
            }
        }
        return super.damage(source, amount);
    }

    @Override
    public void tickMovement() {
        super.tickMovement();

        // 持续监控目标，确保不攻击受保护的实体
        if (this.getCommanderId() != null && this.getTarget() != null) {
            LivingEntity currentTarget = this.getTarget();

            // 检查当前目标是否应该被保护
            if (!this.canTarget(currentTarget)) {
                System.out.println("EntityDreadMob检测到不当目标，清除: " + currentTarget.getClass().getSimpleName());
                super.setTarget(null); // 使用super避免递归
                this.setAttacker(null);
            }
        }

        // 原有的指挥官目标同步逻辑（只在非召唤状态下）
        if (this.getCommanderId() == null && !this.getWorld().isClient && this.getCommander() instanceof EntityDreadLich lich) {
            if (lich.getTarget() != null && lich.getTarget().isAlive()) {
                this.setTarget(lich.getTarget());
            }
        }
    }

    @Override
    public Entity getCommander() {
        try {
            UUID uuid = this.getCommanderId();
            LivingEntity player = uuid == null ? null : this.getWorld().getPlayerByUuid(uuid);
            if (player != null) return player;
            else {
                if (!this.getWorld().isClient) {
                    Entity entity = this.getWorld().getServer().getWorld(this.getWorld().getRegistryKey()).getEntity(uuid);
                    if (entity instanceof LivingEntity) {
                        return entity;
                    }
                }
            }
        } catch (IllegalArgumentException var2) {
            return null;
        }
        return null;
    }

    public void onKillEntity(LivingEntity LivingEntityIn) {
        Entity commander = this instanceof EntityDreadLich ? this : this.getCommander();
        if (commander != null && !(LivingEntityIn instanceof EntityDragonBase)) {// zombie dragons!!!!
            Entity summoned = necromancyEntity(LivingEntityIn);
            if (summoned != null) {
                summoned.copyPositionAndRotation(LivingEntityIn);
                if (!this.getWorld().isClient)
                    this.getWorld().spawnEntity(summoned);
                if (commander instanceof EntityDreadLich lich)
                    lich.setMinionCount(lich.getMinionCount() + 1);
                if (summoned instanceof EntityDreadMob mob) {
                    // 如果当前生物是玩家召唤的，新召唤的生物也从属于玩家
                    if (this.getCommanderId() != null) {
                        mob.setCommanderId(this.getCommanderId()); // 设置为玩家的UUID
                        mob.setMinionMode(this.getMinionMode()); // 继承当前AI模式
                        // 设置召唤者尸巫ID（如果当前是尸巫召唤的）
                        if (this instanceof com.iafenvoy.iceandfire.entity.EntityDreadLich) {
                            mob.setSummonerLichId(this.getUuid()); // 设置为当前尸巫的UUID
                        } else if (this.getSummonerLichId() != null) {
                            mob.setSummonerLichId(this.getSummonerLichId()); // 继承召唤者尸巫ID
                        }
                        // 移除时间限制，让间接召唤的生物也永久存在
                        // mob.setSummonedLifetime(this.getSummonedLifetime()); // 已移除时间限制
                        System.out.println("尸巫召唤的生物设置为玩家召唤物: " + mob.getClass().getSimpleName());
                    } else {
                        mob.setCommanderId(commander.getUuid()); // 原有逻辑：设置为尸巫
                    }
                }
            }
        }

    }

    @Override
    public void remove(RemovalReason reason) {
        if (!this.isRemoved() && this.getCommander() != null && this.getCommander() instanceof EntityDreadLich lich)
            lich.setMinionCount(lich.getMinionCount() - 1);
        super.remove(reason);
    }

    @Override
    public void onDeath(DamageSource damageSource) {
        // 如果是召唤生物且死亡，检查是否应该复活
        if (this.getCommanderId() != null && !this.getWorld().isClient) {
            // 检查是否是高级召唤物（尸巫）或者复活源是否还存在
            if (shouldRevive()) {
                reviveSummonedMob();
                return; // 阻止正常的死亡流程
            }
        }

        super.onDeath(damageSource);
    }

    private boolean shouldRevive() {
        // 尸巫不复活（它们是高级召唤物）
        if (this instanceof com.iafenvoy.iceandfire.entity.EntityDreadLich) {
            System.out.println("惊悚尸巫死亡，不复活");
            return false;
        }

        // 检查召唤该生物的特定尸巫是否还活着
        UUID summonerLichId = this.getSummonerLichId();
        if (summonerLichId != null) {
            // 在世界中寻找特定的召唤者尸巫
            var allLiches = this.getWorld().getEntitiesByClass(
                com.iafenvoy.iceandfire.entity.EntityDreadLich.class,
                this.getBoundingBox().expand(200), // 200格范围内
                lich -> lich.getUuid().equals(summonerLichId) && lich.isAlive()
            );

            if (allLiches.isEmpty()) {
                System.out.println("召唤者尸巫已死亡，" + this.getClass().getSimpleName() + " 不复活");
                return false;
            } else {
                System.out.println("召唤者尸巫仍存活，" + this.getClass().getSimpleName() + " 可以复活");
                return true;
            }
        }

        // 如果没有召唤者尸巫ID，说明不是尸巫召唤的，不复活
        System.out.println("非尸巫召唤的生物，" + this.getClass().getSimpleName() + " 不复活");
        return false;
    }

    private void reviveSummonedMob() {
        try {
            // 创建相同类型的新实体
            EntityDreadMob revivedMob = (EntityDreadMob) this.getType().create(this.getWorld());
            if (revivedMob != null) {
                // 复制位置和属性
                revivedMob.copyPositionAndRotation(this);
                revivedMob.initialize((net.minecraft.world.ServerWorldAccess) this.getWorld(),
                    this.getWorld().getLocalDifficulty(this.getBlockPos()),
                    net.minecraft.entity.SpawnReason.CONVERSION, null);

                // 保持召唤者信息
                revivedMob.setCommanderId(this.getCommanderId());
                revivedMob.setMinionMode(this.getMinionMode());
                revivedMob.setSummonerLichId(this.getSummonerLichId()); // 复制召唤者尸巫ID
                // 移除生存时间限制，让复活的生物永久存在
                // revivedMob.setSummonedLifetime(this.getSummonedLifetime());

                // 设置满血状态
                revivedMob.setHealth(revivedMob.getMaxHealth());

                // 复制自定义名称
                if (this.hasCustomName()) {
                    revivedMob.setCustomName(this.getCustomName());
                    revivedMob.setCustomNameVisible(this.isCustomNameVisible());
                }

                // 生成复活后的实体
                this.getWorld().spawnEntity(revivedMob);

                // 播放复活效果
                this.getWorld().playSound(null, this.getBlockPos(),
                    net.minecraft.sound.SoundEvents.ENTITY_ZOMBIE_VILLAGER_CURE,
                    net.minecraft.sound.SoundCategory.HOSTILE, 1.0F, 0.8F);

                // 移除当前实体（避免重复）
                this.discard();

                System.out.println("召唤生物复活: " + this.getClass().getSimpleName());
            }
        } catch (Exception e) {
            System.err.println("召唤生物复活失败: " + e.getMessage());
            super.onDeath(null); // 如果复活失败，正常死亡
        }
    }
}
