accessWidener v2 named
accessible field net/minecraft/client/render/entity/EntityRenderDispatcher renderers Ljava/util/Map;
accessible field net/minecraft/entity/mob/MobEntity goalSelector Lnet/minecraft/entity/ai/goal/GoalSelector;
accessible field net/minecraft/entity/mob/MobEntity targetSelector Lnet/minecraft/entity/ai/goal/GoalSelector;
accessible field net/minecraft/entity/projectile/TridentEntity LOYALTY Lnet/minecraft/entity/data/TrackedData;
accessible field net/minecraft/entity/projectile/TridentEntity ENCHANTED Lnet/minecraft/entity/data/TrackedData;
accessible field net/minecraft/entity/projectile/TridentEntity dealtDamage Z
accessible field net/minecraft/structure/pool/StructurePool elements Lit/unimi/dsi/fastutil/objects/ObjectArrayList;
accessible field net/minecraft/structure/pool/StructurePool elementCounts Ljava/util/List;
mutable field net/minecraft/structure/pool/StructurePool elements Lit/unimi/dsi/fastutil/objects/ObjectArrayList;
mutable field net/minecraft/structure/pool/StructurePool elementCounts Ljava/util/List;
accessible field net/minecraft/world/World damageSources Lnet/minecraft/entity/damage/DamageSources;
accessible method net/minecraft/entity/LivingEntity knockback (Lnet/minecraft/entity/LivingEntity;)V
accessible field net/minecraft/entity/ai/pathing/PathNodeMaker entityBlockXSize I
accessible field net/minecraft/entity/ai/pathing/PathNodeMaker entityBlockYSize I
accessible field net/minecraft/entity/ai/pathing/PathNodeMaker entityBlockZSize I
accessible class net/minecraft/client/render/RenderPhase$DepthTest
mutable field net/minecraft/client/render/RenderPhase beginAction Ljava/lang/Runnable;
accessible method net/minecraft/entity/damage/DamageTracker getBiggestFall ()Lnet/minecraft/entity/damage/DamageRecord;
accessible method net/minecraft/client/gui/DrawContext drawTexturedQuad (Lnet/minecraft/util/Identifier;IIIIIFFFFFFFF)V
accessible class net/minecraft/client/render/model/json/Transformation$Deserializer
accessible method net/minecraft/client/render/model/json/Transformation$Deserializer <init> ()V
accessible class net/minecraft/client/render/model/json/ModelTransformation$Deserializer
accessible method net/minecraft/client/render/model/json/ModelTransformation$Deserializer <init> ()V
accessible field net/minecraft/client/gui/screen/Screen drawables Ljava/util/List;
accessible field net/minecraft/world/poi/PointOfInterestTypes POI_STATES_TO_TYPE Ljava/util/Map;
accessible method net/minecraft/client/render/GameRenderer loadPostProcessor (Lnet/minecraft/util/Identifier;)V
accessible method net/minecraft/client/render/Camera clipToSpace (F)F
accessible method net/minecraft/client/render/Camera moveBy (FFF)V
accessible method net/minecraft/particle/SimpleParticleType <init> (Z)V
accessible field net/minecraft/entity/projectile/PersistentProjectileEntity stack Lnet/minecraft/item/ItemStack;
accessible method net/minecraft/block/Blocks createLeavesBlock (Lnet/minecraft/sound/BlockSoundGroup;)Lnet/minecraft/block/Block;
accessible method net/minecraft/entity/SpawnRestriction register (Lnet/minecraft/entity/EntityType;Lnet/minecraft/entity/SpawnLocation;Lnet/minecraft/world/Heightmap$Type;Lnet/minecraft/entity/SpawnRestriction$SpawnPredicate;)V
extendable method net/minecraft/entity/LivingEntity getDimensions (Lnet/minecraft/entity/EntityPose;)Lnet/minecraft/entity/EntityDimensions;
accessible method net/minecraft/entity/LivingEntity getXpToDrop ()I
accessible method net/minecraft/entity/projectile/PersistentProjectileEntity setPierceLevel (B)V
accessible method net/minecraft/client/particle/ParticleManager registerFactory (Lnet/minecraft/particle/ParticleType;Lnet/minecraft/client/particle/ParticleFactory;)V
accessible method net/minecraft/client/particle/ParticleManager registerFactory (Lnet/minecraft/particle/ParticleType;Lnet/minecraft/client/particle/ParticleManager$SpriteAwareFactory;)V
accessible class net/minecraft/client/particle/ParticleManager$SpriteAwareFactory
accessible method net/minecraft/client/gui/screen/ingame/HandledScreens register (Lnet/minecraft/screen/ScreenHandlerType;Lnet/minecraft/client/gui/screen/ingame/HandledScreens$Provider;)V
accessible class net/minecraft/client/gui/screen/ingame/HandledScreens$Provider
accessible field net/minecraft/entity/ai/pathing/PathNodeMaker entity Lnet/minecraft/entity/mob/MobEntity;
accessible field net/minecraft/client/world/ClientWorld entityManager Lnet/minecraft/world/entity/ClientEntityManager;
