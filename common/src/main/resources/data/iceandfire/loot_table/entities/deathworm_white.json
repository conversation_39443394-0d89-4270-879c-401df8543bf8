{"pools": [{"name": "deathworm_white", "rolls": 3, "entries": [{"type": "item", "name": "iceandfire:deathworm_chitin_white", "weight": 30, "functions": [{"function": "set_count", "count": {"type": "minecraft:uniform", "min": 0, "max": 2}}, {"enchantment": "minecraft:looting", "function": "minecraft:enchanted_count_increase", "count": {"type": "minecraft:uniform", "min": 0, "max": 1}}]}, {"type": "item", "name": "minecraft:rotten_flesh", "weight": 30, "functions": [{"function": "set_count", "count": {"type": "minecraft:uniform", "min": -1, "max": 2}}, {"enchantment": "minecraft:looting", "function": "minecraft:enchanted_count_increase", "count": {"type": "minecraft:uniform", "min": 0, "max": 1}}]}, {"type": "item", "name": "iceandfire:deathworm_egg", "weight": 10, "functions": [{"function": "set_count", "count": {"type": "minecraft:uniform", "min": -2, "max": 1}}, {"enchantment": "minecraft:looting", "function": "minecraft:enchanted_count_increase", "count": {"type": "minecraft:uniform", "min": 0, "max": 1}}]}]}, {"name": "deathworm_white", "rolls": 3, "entries": [{"type": "item", "name": "iceandfire:deathworm_chitin_white", "weight": 30, "functions": [{"function": "set_count", "count": {"type": "minecraft:uniform", "min": 0, "max": 2}}, {"enchantment": "minecraft:looting", "function": "minecraft:enchanted_count_increase", "count": {"type": "minecraft:uniform", "min": 0, "max": 2}}]}, {"type": "item", "name": "minecraft:rotten_flesh", "weight": 30, "functions": [{"function": "set_count", "count": {"type": "minecraft:uniform", "min": -1, "max": 2}}, {"enchantment": "minecraft:looting", "function": "minecraft:enchanted_count_increase", "count": {"type": "minecraft:uniform", "min": 0, "max": 1}}]}, {"type": "item", "name": "iceandfire:deathworm_egg_giant", "weight": 5, "functions": [{"function": "set_count", "count": {"type": "minecraft:uniform", "min": -2, "max": 1}}, {"enchantment": "minecraft:looting", "function": "minecraft:enchanted_count_increase", "count": {"type": "minecraft:uniform", "min": 0, "max": 1}}]}]}, {"name": "deathworm_white", "conditions": [{"condition": "killed_by_player"}, {"condition": "minecraft:random_chance_with_enchanted_bonus", "enchanted_chance": {"type": "minecraft:linear", "base": 0.035, "per_level_above_first": 0.01}, "enchantment": "minecraft:looting", "unenchanted_chance": 0.025}], "rolls": 1, "entries": [{"type": "item", "name": "iceandfire:deathworm_tounge", "weight": 1}]}]}