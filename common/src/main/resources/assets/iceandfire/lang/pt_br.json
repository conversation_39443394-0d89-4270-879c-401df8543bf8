{"advancements.iceandfire.bestiary.description": "Usando 3 manuscritos encontrados em masmorras, crie um bestiário", "advancements.iceandfire.bestiary.title": "Livro dos Monstros", "advancements.iceandfire.deathworm_egg.description": "Obtenha um ovo de verme da morte", "advancements.iceandfire.deathworm_egg.title": "<PERSON>", "advancements.iceandfire.dragon_egg.description": "Obtenha um ovo de dragão", "advancements.iceandfire.dragon_egg.title": "<PERSON><PERSON><PERSON> dos Dragões", "advancements.iceandfire.dragon_flute.description": "Crie uma flauta de dragão para parar dragões em fuga", "advancements.iceandfire.dragon_flute.title": "A Canção dos Dragões", "advancements.iceandfire.dragon_forge_brick.description": "Crie um tijolo de forja de dragão", "advancements.iceandfire.dragon_forge_brick.title": "Outro Tijolo na Parede", "advancements.iceandfire.dragon_forge_core.description": "Crie um núcleo de forja de dragão", "advancements.iceandfire.dragon_forge_core.title": "O Coração e a Chama", "advancements.iceandfire.dragon_horn.description": "Crie um chifre de dragão para armazenar dragões", "advancements.iceandfire.dragon_horn.title": "Um Rugido <PERSON>", "advancements.iceandfire.dragon_meal.description": "Crie Comida de Dragão para crescer rapidamente os dragões bebês", "advancements.iceandfire.dragon_meal.title": "Hormônio de Crescimento de Dragão", "advancements.iceandfire.dragon_stick.description": "Crie um cajado de comando de dragão para controlar seus dragões", "advancements.iceandfire.dragon_stick.title": "Comandante de Dragão", "advancements.iceandfire.dragonarmor.description": "Obtenha uma armadura de <PERSON>", "advancements.iceandfire.dragonarmor.title": "Hora de se Equipar", "advancements.iceandfire.dragonbone_sword_fire.description": "Crie a lendária arma de dragão de fogo, gelo ou raio", "advancements.iceandfire.dragonbone_sword_fire.title": "<PERSON><PERSON>", "advancements.iceandfire.dragonbone_tool.description": "Crie uma ferramenta feita de osso de drag<PERSON>", "advancements.iceandfire.dragonbone_tool.title": "Estilhaçamento II", "advancements.iceandfire.dragonsteel.description": "Crie um lingote de aço de dragão com sangue de dragão e ferro", "advancements.iceandfire.dragonsteel.title": "<PERSON><PERSON><PERSON>", "advancements.iceandfire.dragonsteel_weapon.description": "Crie uma arma de aço de dragão", "advancements.iceandfire.dragonsteel_weapon.title": "Evolução Dracônica", "advancements.iceandfire.gorgon_head.description": "Obtenha a cabeça de górgona, uma arma que transforma em pedra quem olhar para ela", "advancements.iceandfire.gorgon_head.title": "Fúria de Titãs", "advancements.iceandfire.jar_pixie.description": "Capture uma pixie em um frasco para farmar o pó", "advancements.iceandfire.jar_pixie.title": "<PERSON><PERSON><PERSON>", "advancements.iceandfire.kill_a_dragon.description": "Mate um dragão de gelo, fogo ou raio", "advancements.iceandfire.kill_a_dragon.title": "Caçador de Dragões", "advancements.iceandfire.kill_cyclops.description": "Derrote um ciclope", "advancements.iceandfire.kill_cyclops.title": "Meu Nome é Ninguém", "advancements.iceandfire.kill_deathworm.description": "Mate um verme da morte. Dica profissional: eles adoram comer TNT!", "advancements.iceandfire.kill_deathworm.title": "Os Nadadores do Deserto", "advancements.iceandfire.kill_ghost.description": "Mate um fantasma.", "advancements.iceandfire.kill_ghost.title": "Quem Você V<PERSON>?", "advancements.iceandfire.kill_hydra.description": "Mate uma hidra.", "advancements.iceandfire.kill_hydra.title": "Os Doze <PERSON>rabal<PERSON> de Hércules", "advancements.iceandfire.kill_sea_serpent.description": "Mate uma serpente marinha.", "advancements.iceandfire.kill_sea_serpent.title": "Mar? <PERSON><PERSON>", "advancements.iceandfire.kill_siren.description": "Resista à sua canção e mate uma sereia", "advancements.iceandfire.kill_siren.title": "<PERSON>re-me ao Mastro", "advancements.iceandfire.kill_stymphalian_bird.description": "Mate um pássaro estin<PERSON>o", "advancements.iceandfire.kill_stymphalian_bird.title": "Hércules Estava com Tudo!", "advancements.iceandfire.kill_troll.description": "Mate um troll", "advancements.iceandfire.kill_troll.title": "Eles Têm um Troll da Caverna!", "advancements.iceandfire.lectern.description": "Crie um Púlpito de Bestiário para aprender mais sobre o mundo de Ice and Fire", "advancements.iceandfire.lectern.title": "Gostaria de Saber Mais?", "advancements.iceandfire.pixie_wand.description": "Crie a lendária arma de pixie", "advancements.iceandfire.pixie_wand.title": "Onde Está Minha Mente?", "advancements.iceandfire.root.description": "Entre em um mundo de criaturas mágicas", "advancements.iceandfire.root.title": "Ice and Fire", "advancements.iceandfire.rotten_egg.description": "Obtenha um ovo podre", "advancements.iceandfire.rotten_egg.title": "Podre até o Caroço", "advancements.iceandfire.stymphalian_arrow.description": "Crie uma flecha com penas de pássaro estinfaliano que voa mais longe", "advancements.iceandfire.stymphalian_arrow.title": "<PERSON><PERSON><PERSON>", "advancements.iceandfire.tame_amphithere.description": "Dome um anfitérion atirando nele e montando-o", "advancements.iceandfire.tame_amphithere.title": "V<PERSON><PERSON><PERSON> da Selva", "advancements.iceandfire.tame_cockatrice.description": "Dome um cocatrice de um ovo ou olhando para ele repetidamente", "advancements.iceandfire.tame_cockatrice.title": "Fique Fora da Minha Visão Periférica!", "advancements.iceandfire.tame_hippocampus.description": "Dome um hipocampo alimentando-o com algas", "advancements.iceandfire.tame_hippocampus.title": "Free Willy", "advancements.iceandfire.tame_hippogryph.description": "Dome um hipogrifo jogando pés de coelho perto dele", "advancements.iceandfire.tame_hippogryph.title": "Bicuço", "advancements.iceandfire.tame_pixie.description": "Faça amizade com uma pixie jogando bolo perto dela", "advancements.iceandfire.tame_pixie.title": "Aí Vem o Seu Homem", "advancements.iceandfire.tide_trident.description": "Jogue um tridente da maré em um mob", "advancements.iceandfire.tide_trident.title": "<PERSON><PERSON><PERSON>, Deus dos Mares", "amphithere.command.0": "Este anfitérion está vagando.", "amphithere.command.1": "<PERSON>ste anfitérion está sentado.", "amphithere.command.2": "<PERSON>ste anfitérion está seguindo.", "amphithere.command.new_home": "A posição inicial deste anfitérion foi definida para %d, %d, %d.", "bestiary.alchemy": "Alquimia de Sangue", "bestiary.amphithere": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bestiary.cockatrice": "Coca<PERSON>es", "bestiary.contains": "Contém:", "bestiary.cyclops": "Ciclopes", "bestiary.deathworm": "<PERSON><PERSON><PERSON>", "bestiary.dragonforge": "Forja de Dragão", "bestiary.dread_mobs": "Os Mortos-Vivos", "bestiary.firedragon": "Dragão de Fogo", "bestiary.firedragonegg": "Ovos de Dragão de Fogo", "bestiary.ghost": "Fantasma", "bestiary.gorgon": "Górgonas", "bestiary.hippocampus": "Hipocampos", "bestiary.hippogryph": "Hipogri<PERSON>s", "bestiary.hold_shift": "Segure §e[SHIFT]§r§7 para ver o conteúdo", "bestiary.hydra": "<PERSON><PERSON>", "bestiary.icedragon": "Dragão de Gelo", "bestiary.icedragonegg": "Ovos de Dragão de Gelo", "bestiary.introduction": "Introdução", "bestiary.lightningdragon": "Dragão do Raio", "bestiary.lightningdragonegg": "Ovos de Dragão do Raio", "bestiary.materials": "Materiais de Dragão", "bestiary.pixie": "Pixies", "bestiary.seaserpent": "<PERSON><PERSON><PERSON>", "bestiary.siren": "<PERSON><PERSON><PERSON>", "bestiary.stymphalianbird": "<PERSON><PERSON><PERSON><PERSON>", "bestiary.tameddragons": "<PERSON><PERSON><PERSON><PERSON>", "bestiary.troll": "Trolls", "bestiary.villagers": "Aldeões da Neve", "bestiary_gui": "<PERSON><PERSON><PERSON><PERSON>", "biome.iceandfire.dread_forest": "Floresta So<PERSON>", "biome.iceandfire.dread_plain": "<PERSON><PERSON><PERSON>", "block.iceandfire.ash": "Cinza", "block.iceandfire.burnt_torch": "Tocha Apagada", "block.iceandfire.burnt_torch_wall": "Tocha Apagada", "block.iceandfire.chared_cobblestone": "Pedregu<PERSON><PERSON>", "block.iceandfire.chared_dirt": "Terra Carbonizada", "block.iceandfire.chared_dirt_path": "Caminho de Terra Carbonizada", "block.iceandfire.chared_grass": "Grama Carbonizada", "block.iceandfire.chared_gravel": "<PERSON><PERSON><PERSON><PERSON>", "block.iceandfire.chared_stone": "Pedra Carbonizada", "block.iceandfire.copper_pile": "<PERSON><PERSON><PERSON>", "block.iceandfire.crackled_cobblestone": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.iceandfire.crackled_dirt": "Terra Rachada", "block.iceandfire.crackled_dirt_path": "<PERSON><PERSON><PERSON> de Terra Rachada", "block.iceandfire.crackled_grass": "Grama Rachada", "block.iceandfire.crackled_gravel": "<PERSON><PERSON><PERSON><PERSON>", "block.iceandfire.crackled_stone": "<PERSON><PERSON><PERSON>", "block.iceandfire.deepslate_silver_ore": "Minério de Prata de Ardósia Profunda", "block.iceandfire.dragon_bone_block": "Bloco de Ossos de Dragão", "block.iceandfire.dragon_bone_wall": "Muro de Osso de Dragão", "block.iceandfire.dragon_ice": "Gelo de Dragão", "block.iceandfire.dragon_ice_spikes": "Espigões de Gelo de Dragão", "block.iceandfire.dragonforge_fire_brick": "Tijolo de Forja de Dragão de Fogo", "block.iceandfire.dragonforge_fire_core": "Núcleo de Forja de Dragão de Fogo", "block.iceandfire.dragonforge_fire_core_disabled": "Núcleo de Forja de Dragão de Fogo", "block.iceandfire.dragonforge_fire_input": "Abertura de Forja de Dragão de Fogo", "block.iceandfire.dragonforge_ice_brick": "Tijolo de Forja de Dragão de Gelo", "block.iceandfire.dragonforge_ice_core": "Núcleo de Forja de Dragão de Gelo", "block.iceandfire.dragonforge_ice_core_disabled": "Núcleo de Forja de Dragão de Gelo", "block.iceandfire.dragonforge_ice_input": "Abertura de Forja de Dragão de Gelo", "block.iceandfire.dragonforge_lightning_brick": "Tijolo de Forja de Dragão do Raio", "block.iceandfire.dragonforge_lightning_core": "Núcleo de Forja de Dragão do Raio", "block.iceandfire.dragonforge_lightning_core_disabled": "Núcleo de Forja de Dragão do Raio", "block.iceandfire.dragonforge_lightning_input": "Abertura de Forja de Dragão do Raio", "block.iceandfire.dragonscale_amethyst": "Bloco de Escamas de Dragão", "block.iceandfire.dragonscale_black": "Bloco de Escamas de Dragão", "block.iceandfire.dragonscale_blue": "Bloco de Escamas de Dragão", "block.iceandfire.dragonscale_bronze": "Bloco de Escamas de Dragão", "block.iceandfire.dragonscale_copper": "Bloco de Escamas de Dragão", "block.iceandfire.dragonscale_electric": "Bloco de Escamas de Dragão", "block.iceandfire.dragonscale_gray": "Bloco de Escamas de Dragão", "block.iceandfire.dragonscale_green": "Bloco de Escamas de Dragão", "block.iceandfire.dragonscale_red": "Bloco de Escamas de Dragão", "block.iceandfire.dragonscale_sapphire": "Bloco de Escamas de Dragão", "block.iceandfire.dragonscale_silver": "Bloco de Escamas de Dragão", "block.iceandfire.dragonscale_white": "Bloco de Escamas de Dragão", "block.iceandfire.dragonsteel_fire_block": "Bloco de Aço de Dragão de Fogo", "block.iceandfire.dragonsteel_ice_block": "Bloco de Aço de Dragão de Gelo", "block.iceandfire.dragonsteel_lightning_block": "Bloco de Aço de Dragão do Raio", "block.iceandfire.dread_portal": "Portal das Terras Sombrias", "block.iceandfire.dread_spawner": "Gerador de Mobs de Pedra Sombria", "block.iceandfire.dread_stone": "Pedra Sombria", "block.iceandfire.dread_stone_bricks": "<PERSON><PERSON><PERSON>los de Pedra Sombria", "block.iceandfire.dread_stone_bricks_chiseled": "Tijolos de Pedra Sombria Cinzelados", "block.iceandfire.dread_stone_bricks_cracked": "Tijolos de Pedra Sombria Rachados", "block.iceandfire.dread_stone_bricks_mossy": "Tijolos de Pedra Sombria Musgosos", "block.iceandfire.dread_stone_face": "<PERSON><PERSON><PERSON><PERSON> de Pedra Sombria", "block.iceandfire.dread_stone_slab": "<PERSON><PERSON> de Tijolos de Pedra Sombria", "block.iceandfire.dread_stone_stairs": "Escadas de Tijolos de Pedra Sombria", "block.iceandfire.dread_stone_tile": "<PERSON>dril<PERSON> de Pedra Sombria", "block.iceandfire.dread_torch": "<PERSON>cha de Pedra Sombria", "block.iceandfire.dread_torch_wall": "<PERSON>cha de Pedra Sombria", "block.iceandfire.dreadwood_leaves": "<PERSON><PERSON><PERSON> de Madeira Sombria", "block.iceandfire.dreadwood_log": "Tora de Madeira Sombria", "block.iceandfire.dreadwood_planks": "Tábuas de Madeira Sombria", "block.iceandfire.dreadwood_planks_lock": "Fechadura de Madeira Sombria", "block.iceandfire.dreadwood_sapling": "<PERSON>da de Madeira Sombria", "block.iceandfire.egginice": "Ovo de Dragão Congelado", "block.iceandfire.fire_lily": "<PERSON><PERSON><PERSON>", "block.iceandfire.frost_lily": "<PERSON><PERSON><PERSON>", "block.iceandfire.frozen_cobblestone": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.iceandfire.frozen_dirt": "Terra Congelada", "block.iceandfire.frozen_dirt_path": "<PERSON><PERSON><PERSON> de Terra Congelada", "block.iceandfire.frozen_grass": "Grama Congelada", "block.iceandfire.frozen_gravel": "<PERSON><PERSON><PERSON><PERSON>", "block.iceandfire.frozen_splinters": "Estilhaços Congelados", "block.iceandfire.frozen_stone": "<PERSON><PERSON><PERSON>", "block.iceandfire.ghost_chest": "<PERSON><PERSON>", "block.iceandfire.gold_pile": "<PERSON><PERSON><PERSON>", "block.iceandfire.graveyard_soil": "Solo de Cemitério", "block.iceandfire.graveyard_soil.desc": "Gera Fantasmas à noite", "block.iceandfire.lectern": "Púlpito de Bestiário", "block.iceandfire.lightning_lily": "Lírio do <PERSON>o", "block.iceandfire.nest": "<PERSON><PERSON><PERSON>", "block.iceandfire.pixie_house_birch": "Cabana de Pixie de Bétula", "block.iceandfire.pixie_house_dark_oak": "Cabana de Pixie de Carvalho Escuro", "block.iceandfire.pixie_house_mushroom_brown": "Cabana de Pixie de Cogumelo <PERSON>", "block.iceandfire.pixie_house_mushroom_red": "Cabana de Pixie de Cogumelo Vermelho", "block.iceandfire.pixie_house_oak": "Cabana de Pixie de Carvalho", "block.iceandfire.pixie_house_spruce": "Cabana de Pixie de Pinheiro", "block.iceandfire.pixie_jar_0": "Frasco de Pixie Rosa", "block.iceandfire.pixie_jar_1": "Frasco de Pixie Roxa", "block.iceandfire.pixie_jar_2": "Frasco de Pixie Azul", "block.iceandfire.pixie_jar_3": "Frasco de Pixie Verde", "block.iceandfire.pixie_jar_4": "Frasco de Pixie Amarela", "block.iceandfire.pixie_jar_empty": "<PERSON><PERSON>", "block.iceandfire.podium": "P<PERSON><PERSON>", "block.iceandfire.podium_acacia": "Pódio de Acácia", "block.iceandfire.podium_birch": "Pódio de Bétula", "block.iceandfire.podium_dark_oak": "Pódio de Carvalho Escuro", "block.iceandfire.podium_jungle": "P<PERSON><PERSON> da Selva", "block.iceandfire.podium_oak": "Pódio de Carvalho", "block.iceandfire.podium_spruce": "Pódio de Pinheiro", "block.iceandfire.raw_silver_block": "Bloco de Prata Bruta", "block.iceandfire.sapphire_block": "Bloco de Safira", "block.iceandfire.sapphire_ore": "<PERSON><PERSON><PERSON>", "block.iceandfire.sea_serpent_scale_block": "Bloco de Escamas de <PERSON>", "block.iceandfire.sea_serpent_scale_block_blue": "Bloco de Escamas de <PERSON>", "block.iceandfire.sea_serpent_scale_block_bronze": "Bloco de Escamas de <PERSON>", "block.iceandfire.sea_serpent_scale_block_deepblue": "Bloco de Escamas de <PERSON>", "block.iceandfire.sea_serpent_scale_block_green": "Bloco de Escamas de <PERSON>", "block.iceandfire.sea_serpent_scale_block_purple": "Bloco de Escamas de <PERSON>", "block.iceandfire.sea_serpent_scale_block_red": "Bloco de Escamas de <PERSON>", "block.iceandfire.sea_serpent_scale_block_teal": "Bloco de Escamas de <PERSON>", "block.iceandfire.silver_block": "Bloco de Prata", "block.iceandfire.silver_ore": "<PERSON><PERSON><PERSON>", "block.iceandfire.silver_pile": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.amphithere.black": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.amphithere.blue": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.amphithere.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.amphithere.cyan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.amphithere.gray": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.amphithere.green": "Anfitérion Verde", "block.minecraft.banner.amphithere.light_blue": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.amphithere.lime": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.amphithere.magenta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.amphithere.orange": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.amphithere.pink": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.amphithere.purple": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.amphithere.red": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.amphithere.silver": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.amphithere.white": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.amphithere.yellow": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.bird.black": "<PERSON>ás<PERSON><PERSON>", "block.minecraft.banner.bird.blue": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.bird.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.bird.cyan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.bird.gray": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.bird.green": "Pássaro Verde", "block.minecraft.banner.bird.light_blue": "Pássaro <PERSON>", "block.minecraft.banner.bird.lime": "Pássaro <PERSON>", "block.minecraft.banner.bird.magenta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.bird.orange": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.bird.pink": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.bird.purple": "Pássar<PERSON>", "block.minecraft.banner.bird.red": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.bird.silver": "<PERSON>ássar<PERSON>", "block.minecraft.banner.bird.white": "Pássaro Bran<PERSON>", "block.minecraft.banner.bird.yellow": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.dread.black": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.dread.blue": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.dread.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.dread.cyan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.dread.gray": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.dread.green": "Crânio Sombrio Verde", "block.minecraft.banner.dread.light_blue": "<PERSON><PERSON><PERSON><PERSON> Sombrio Azul Claro", "block.minecraft.banner.dread.lime": "Crân<PERSON>", "block.minecraft.banner.dread.magenta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.dread.orange": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.dread.pink": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.dread.purple": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.dread.red": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.dread.silver": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.dread.white": "<PERSON><PERSON><PERSON><PERSON> Bran<PERSON>", "block.minecraft.banner.dread.yellow": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.eye.black": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.eye.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.eye.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.eye.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.eye.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.eye.green": "Olho Verde", "block.minecraft.banner.eye.light_blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.eye.lime": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.eye.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.eye.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.eye.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.eye.purple": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.eye.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.eye.silver": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.eye.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.eye.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.fae.black": "Fae Preta", "block.minecraft.banner.fae.blue": "<PERSON>ae <PERSON>", "block.minecraft.banner.fae.brown": "<PERSON><PERSON>", "block.minecraft.banner.fae.cyan": "<PERSON>ae <PERSON>", "block.minecraft.banner.fae.gray": "<PERSON><PERSON>", "block.minecraft.banner.fae.green": "Fae Verde", "block.minecraft.banner.fae.light_blue": "Fae Azul <PERSON>", "block.minecraft.banner.fae.lime": "Fae Lima", "block.minecraft.banner.fae.magenta": "<PERSON>ae <PERSON>a", "block.minecraft.banner.fae.orange": "<PERSON><PERSON>", "block.minecraft.banner.fae.pink": "<PERSON><PERSON>", "block.minecraft.banner.fae.purple": "<PERSON>ae R<PERSON>a", "block.minecraft.banner.fae.red": "<PERSON><PERSON>", "block.minecraft.banner.fae.silver": "Fae Prateada", "block.minecraft.banner.fae.white": "Fae Branca", "block.minecraft.banner.fae.yellow": "<PERSON><PERSON>", "block.minecraft.banner.feather.black": "Pena Preta", "block.minecraft.banner.feather.blue": "Pena <PERSON>", "block.minecraft.banner.feather.brown": "<PERSON><PERSON>", "block.minecraft.banner.feather.cyan": "Pena Ciano", "block.minecraft.banner.feather.gray": "<PERSON><PERSON>", "block.minecraft.banner.feather.green": "Pena Verde", "block.minecraft.banner.feather.light_blue": "Pena Azul Claro", "block.minecraft.banner.feather.lime": "Pena Lima", "block.minecraft.banner.feather.magenta": "<PERSON><PERSON>", "block.minecraft.banner.feather.orange": "<PERSON><PERSON>", "block.minecraft.banner.feather.pink": "<PERSON><PERSON>", "block.minecraft.banner.feather.purple": "<PERSON><PERSON>", "block.minecraft.banner.feather.red": "<PERSON><PERSON>", "block.minecraft.banner.feather.silver": "Pena Prateada", "block.minecraft.banner.feather.white": "Pena Branca", "block.minecraft.banner.feather.yellow": "<PERSON><PERSON>", "block.minecraft.banner.fire.black": "Dragão de Fogo Preto", "block.minecraft.banner.fire.blue": "Dragão de Fogo Azul", "block.minecraft.banner.fire.brown": "Dragão de Fogo Marrom", "block.minecraft.banner.fire.cyan": "Dragão de Fogo Ciano", "block.minecraft.banner.fire.gray": "Dragão de Fogo Cinza", "block.minecraft.banner.fire.green": "Dragão de Fogo Verde", "block.minecraft.banner.fire.light_blue": "Dragão de Fogo Azul Claro", "block.minecraft.banner.fire.lime": "Dragão de Fogo Lima", "block.minecraft.banner.fire.magenta": "Dragão de Fogo Magenta", "block.minecraft.banner.fire.orange": "Dragão de Fogo Laranja", "block.minecraft.banner.fire.pink": "Dragão de Fogo Rosa", "block.minecraft.banner.fire.purple": "Dragão de Fogo Roxo", "block.minecraft.banner.fire.red": "Dragão de Fogo Vermelho", "block.minecraft.banner.fire.silver": "Dragão de Fogo Prateado", "block.minecraft.banner.fire.white": "Dragão de Fogo Branco", "block.minecraft.banner.fire.yellow": "Dragão de Fogo Amarelo", "block.minecraft.banner.fire_head.black": "Cabeça de Dragão de Fogo Preta", "block.minecraft.banner.fire_head.blue": "Cabeça de Dragão de Fogo Azul", "block.minecraft.banner.fire_head.brown": "Cabeça de Dragão de Fogo Marrom", "block.minecraft.banner.fire_head.cyan": "Cabeça de Dragão de Fogo Ciano", "block.minecraft.banner.fire_head.gray": "Cabeça de Dragão de Fogo Cinza", "block.minecraft.banner.fire_head.green": "Cabeça de Dragão de Fogo Verde", "block.minecraft.banner.fire_head.light_blue": "Cabeça de Dragão de Fogo Azul Claro", "block.minecraft.banner.fire_head.lime": "Cabeça de Dragão de Fogo Lima", "block.minecraft.banner.fire_head.magenta": "Cabeça de Dragão de Fogo Magenta", "block.minecraft.banner.fire_head.orange": "Cabeça de Dragão de Fogo Laranja", "block.minecraft.banner.fire_head.pink": "Cabeça de Dragão de Fogo Rosa", "block.minecraft.banner.fire_head.purple": "Cabeça de Dragão de Fogo Roxa", "block.minecraft.banner.fire_head.red": "Cabeça de Dragão de Fogo Vermelha", "block.minecraft.banner.fire_head.silver": "Cabeça de Dragão de Fogo Prateada", "block.minecraft.banner.fire_head.white": "Cabeça de Dragão de Fogo Branca", "block.minecraft.banner.fire_head.yellow": "Cabeça de Dragão de Fogo Amarela", "block.minecraft.banner.gorgon.black": "Górgona Preta", "block.minecraft.banner.gorgon.blue": "Górgona Azul", "block.minecraft.banner.gorgon.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.gorgon.cyan": "Górgona Ciano", "block.minecraft.banner.gorgon.gray": "Górgona Cinza", "block.minecraft.banner.gorgon.green": "Górgona Verde", "block.minecraft.banner.gorgon.light_blue": "Górgona Azul Claro", "block.minecraft.banner.gorgon.lime": "Górgona Lima", "block.minecraft.banner.gorgon.magenta": "Górgona Ma<PERSON>", "block.minecraft.banner.gorgon.orange": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.gorgon.pink": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.gorgon.purple": "Górgona Roxa", "block.minecraft.banner.gorgon.red": "Górgona Vermelha", "block.minecraft.banner.gorgon.silver": "Górgona Prateada", "block.minecraft.banner.gorgon.white": "Górgona Branca", "block.minecraft.banner.gorgon.yellow": "Górgona Amarela", "block.minecraft.banner.hippocampus.black": "Hipocampo Preto", "block.minecraft.banner.hippocampus.blue": "Hipocampo Azul", "block.minecraft.banner.hippocampus.brown": "Hipocampo Marrom", "block.minecraft.banner.hippocampus.cyan": "Hipocampo Ciano", "block.minecraft.banner.hippocampus.gray": "Hipocampo Cinza", "block.minecraft.banner.hippocampus.green": "Hipocampo Verde", "block.minecraft.banner.hippocampus.light_blue": "Hipocampo Azul Claro", "block.minecraft.banner.hippocampus.lime": "Hipocampo Lima", "block.minecraft.banner.hippocampus.magenta": "Hipocampo Magenta", "block.minecraft.banner.hippocampus.orange": "Hipocampo <PERSON>", "block.minecraft.banner.hippocampus.pink": "Hipocampo Rosa", "block.minecraft.banner.hippocampus.purple": "Hipocampo Roxo", "block.minecraft.banner.hippocampus.red": "Hipocampo Vermelho", "block.minecraft.banner.hippocampus.silver": "Hipocampo Prateado", "block.minecraft.banner.hippocampus.white": "Hipocampo Branco", "block.minecraft.banner.hippocampus.yellow": "Hipocampo <PERSON>elo", "block.minecraft.banner.hippogryph_head.black": "Cabeça de Hipogrifo Preta", "block.minecraft.banner.hippogryph_head.blue": "Cabeça de Hipogrifo <PERSON>l", "block.minecraft.banner.hippogryph_head.brown": "Cabeça de Hipogrifo Marrom", "block.minecraft.banner.hippogryph_head.cyan": "Cabeça de Hipogrifo <PERSON>", "block.minecraft.banner.hippogryph_head.gray": "Cabeça de Hipogrifo <PERSON>inza", "block.minecraft.banner.hippogryph_head.green": "Cabeça de Hipogrifo Verde", "block.minecraft.banner.hippogryph_head.light_blue": "Cabeça de Hipogrifo Azul Claro", "block.minecraft.banner.hippogryph_head.lime": "Cabeça de Hipogrifo Lima", "block.minecraft.banner.hippogryph_head.magenta": "Cabeça de Hipogrifo <PERSON>", "block.minecraft.banner.hippogryph_head.orange": "Cabeça de Hipogrifo <PERSON>", "block.minecraft.banner.hippogryph_head.pink": "Cabeça de Hipogrifo Rosa", "block.minecraft.banner.hippogryph_head.purple": "Cabeça de Hipogrifo <PERSON>", "block.minecraft.banner.hippogryph_head.red": "Cabeça de Hipogrifo <PERSON>", "block.minecraft.banner.hippogryph_head.silver": "Cabeça de Hipogrifo Prateada", "block.minecraft.banner.hippogryph_head.white": "Cabeça de Hipogrifo Branca", "block.minecraft.banner.hippogryph_head.yellow": "Cabeça de Hipogrifo <PERSON>ela", "block.minecraft.banner.ice.black": "Dragão de Gelo Preto", "block.minecraft.banner.ice.blue": "Dragão de Gelo Azul", "block.minecraft.banner.ice.brown": "Dragão de Gelo Marrom", "block.minecraft.banner.ice.cyan": "Dragão de Gelo Ciano", "block.minecraft.banner.ice.gray": "Dragão de Gelo Cinza", "block.minecraft.banner.ice.green": "Dragão de Gelo Verde", "block.minecraft.banner.ice.light_blue": "Dragão de Gelo Azul Claro", "block.minecraft.banner.ice.lime": "Dragão de Gelo Lima", "block.minecraft.banner.ice.magenta": "Dragão de Gelo Magenta", "block.minecraft.banner.ice.orange": "Dragão de Gelo Laranja", "block.minecraft.banner.ice.pink": "Dragão de Gelo Rosa", "block.minecraft.banner.ice.purple": "Dragão de Gelo Roxo", "block.minecraft.banner.ice.red": "Dragão de Gelo Vermelho", "block.minecraft.banner.ice.silver": "Dragão de Gelo Prateado", "block.minecraft.banner.ice.white": "Dragão de Gelo Branco", "block.minecraft.banner.ice.yellow": "Dragão de Gelo Amarelo", "block.minecraft.banner.ice_head.black": "Cabeça de Dragão de Gelo Preta", "block.minecraft.banner.ice_head.blue": "Cabeça de Dragão de Gelo Azul", "block.minecraft.banner.ice_head.brown": "Cabeça de Dragão de Gelo Marrom", "block.minecraft.banner.ice_head.cyan": "Cabeça de Dragão de Gelo Ciano", "block.minecraft.banner.ice_head.gray": "Cabeça de Dragão de Gelo Cinza", "block.minecraft.banner.ice_head.green": "Cabeça de Dragão de Gelo Verde", "block.minecraft.banner.ice_head.light_blue": "Cabeça de Dragão de Gelo Azul Claro", "block.minecraft.banner.ice_head.lime": "Cabeça de Dragão de Gelo Lima", "block.minecraft.banner.ice_head.magenta": "Cabeça de Dragão de Gelo Magenta", "block.minecraft.banner.ice_head.orange": "Cabeça de Dragão de Gelo Laranja", "block.minecraft.banner.ice_head.pink": "Cabeça de Dragão de Gelo Rosa", "block.minecraft.banner.ice_head.purple": "Cabeça de Dragão de Gelo Roxa", "block.minecraft.banner.ice_head.red": "Cabeça de Dragão de Gelo Vermelha", "block.minecraft.banner.ice_head.silver": "Cabeça de Dragão de Gelo Prateada", "block.minecraft.banner.ice_head.white": "Cabeça de Dragão de Gelo Branca", "block.minecraft.banner.ice_head.yellow": "Cabeça de Dragão de Gelo Amarela", "block.minecraft.banner.lightning.black": "Dragão do Raio Preto", "block.minecraft.banner.lightning.blue": "Dragão do Raio Azul", "block.minecraft.banner.lightning.brown": "Dragão do Raio Marrom", "block.minecraft.banner.lightning.cyan": "Dragão do Raio Ciano", "block.minecraft.banner.lightning.gray": "Dragão do Raio Cinza", "block.minecraft.banner.lightning.green": "Dragão do Raio Verde", "block.minecraft.banner.lightning.light_blue": "Dragão do Raio Azul Claro", "block.minecraft.banner.lightning.lime": "Dragão do Raio Lima", "block.minecraft.banner.lightning.magenta": "Dragão do Raio Magenta", "block.minecraft.banner.lightning.orange": "Dragão do Raio Laranja", "block.minecraft.banner.lightning.pink": "Dragão do Raio Rosa", "block.minecraft.banner.lightning.purple": "Dragão do Raio Roxo", "block.minecraft.banner.lightning.red": "Dragão do Raio Vermelho", "block.minecraft.banner.lightning.silver": "Dragão do Raio Prateado", "block.minecraft.banner.lightning.white": "Dragão do Raio Branco", "block.minecraft.banner.lightning.yellow": "Dragão do Raio Amarelo", "block.minecraft.banner.lightning_head.black": "Cabeça de Dragão do Raio Preta", "block.minecraft.banner.lightning_head.blue": "Cabeça de Dragão do Raio Azul", "block.minecraft.banner.lightning_head.brown": "Cabeça de Dragão do Raio Marrom", "block.minecraft.banner.lightning_head.cyan": "Cabeça de Dragão do Raio Ciano", "block.minecraft.banner.lightning_head.gray": "Cabeça de Dragão do Raio Cinza", "block.minecraft.banner.lightning_head.green": "Cabeça de Dragão do Raio Verde", "block.minecraft.banner.lightning_head.light_blue": "Cabeça de Dragão do Raio Azul Claro", "block.minecraft.banner.lightning_head.lime": "Cabeça de Dragão do Raio Lima", "block.minecraft.banner.lightning_head.magenta": "Cabeça de Dragão do Raio Magenta", "block.minecraft.banner.lightning_head.orange": "Cabeça de Dragão do Raio Laranja", "block.minecraft.banner.lightning_head.pink": "Cabeça de Dragão do Raio Rosa", "block.minecraft.banner.lightning_head.purple": "Cabeça de Dragão do Raio Roxa", "block.minecraft.banner.lightning_head.red": "Cabeça de Dragão do Raio Vermelha", "block.minecraft.banner.lightning_head.silver": "Cabeça de Dragão do Raio Prateada", "block.minecraft.banner.lightning_head.white": "Cabeça de Dragão do Raio Branca", "block.minecraft.banner.lightning_head.yellow": "Cabeça de Dragão do Raio Amarela", "block.minecraft.banner.mermaid.black": "Sereia <PERSON>ta", "block.minecraft.banner.mermaid.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.mermaid.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.mermaid.cyan": "<PERSON>eia <PERSON>", "block.minecraft.banner.mermaid.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.mermaid.green": "Sereia Verde", "block.minecraft.banner.mermaid.light_blue": "Sereia A<PERSON>l <PERSON>o", "block.minecraft.banner.mermaid.lime": "Sereia <PERSON>", "block.minecraft.banner.mermaid.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.mermaid.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.mermaid.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.mermaid.purple": "<PERSON><PERSON><PERSON>a", "block.minecraft.banner.mermaid.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.mermaid.silver": "Sereia <PERSON>", "block.minecraft.banner.mermaid.white": "Sereia Branca", "block.minecraft.banner.mermaid.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.sea_serpent.black": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.sea_serpent.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.sea_serpent.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.sea_serpent.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.sea_serpent.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.sea_serpent.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.sea_serpent.light_blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.sea_serpent.lime": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.sea_serpent.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.sea_serpent.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.sea_serpent.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.sea_serpent.purple": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.sea_serpent.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.sea_serpent.silver": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.sea_serpent.white": "Serpente <PERSON>", "block.minecraft.banner.sea_serpent.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.troll.black": "T<PERSON>", "block.minecraft.banner.troll.blue": "T<PERSON>l", "block.minecraft.banner.troll.brown": "T<PERSON>", "block.minecraft.banner.troll.cyan": "<PERSON><PERSON>", "block.minecraft.banner.troll.gray": "<PERSON><PERSON>", "block.minecraft.banner.troll.green": "Troll Verde", "block.minecraft.banner.troll.light_blue": "Troll A<PERSON>l <PERSON>", "block.minecraft.banner.troll.lime": "Troll Lima", "block.minecraft.banner.troll.magenta": "T<PERSON>", "block.minecraft.banner.troll.orange": "<PERSON><PERSON>", "block.minecraft.banner.troll.pink": "<PERSON><PERSON>", "block.minecraft.banner.troll.purple": "Troll <PERSON>o", "block.minecraft.banner.troll.red": "<PERSON><PERSON>", "block.minecraft.banner.troll.silver": "Troll Prateado", "block.minecraft.banner.troll.white": "Troll Branco", "block.minecraft.banner.troll.yellow": "<PERSON><PERSON>", "block.minecraft.banner.weezer.black": "<PERSON><PERSON>", "block.minecraft.banner.weezer.blue": "We<PERSON>", "block.minecraft.banner.weezer.brown": "We<PERSON>", "block.minecraft.banner.weezer.cyan": "We<PERSON>", "block.minecraft.banner.weezer.gray": "We<PERSON>", "block.minecraft.banner.weezer.green": "Weezer Verde", "block.minecraft.banner.weezer.light_blue": "Weezer <PERSON>", "block.minecraft.banner.weezer.lime": "We<PERSON>", "block.minecraft.banner.weezer.magenta": "We<PERSON>", "block.minecraft.banner.weezer.orange": "We<PERSON>", "block.minecraft.banner.weezer.pink": "We<PERSON>", "block.minecraft.banner.weezer.purple": "We<PERSON>", "block.minecraft.banner.weezer.red": "<PERSON><PERSON>", "block.minecraft.banner.weezer.silver": "<PERSON><PERSON>", "block.minecraft.banner.weezer.white": "We<PERSON>", "block.minecraft.banner.weezer.yellow": "We<PERSON>", "cockatrice.command.0": "Este cocatrice está vagando.", "cockatrice.command.1": "Este cocatrice está sentado.", "cockatrice.command.2": "Este cocatrice está seguindo.", "cockatrice.command.3": "Este cocatrice está patrulhando.", "cockatrice.command.new_home": "A posição inicial deste cocatrice foi definida para %d, %d, %d, %s.", "cockatrice.command.remove_home": "A posição inicial deste cocatrice foi removida.", "config.iceandfire.title": "§km§bIce §fAnd §6Fire §fConfigurações§km", "config.jade.plugin_iceandfire.dragon": "Informações do Dragão", "config.jade.plugin_iceandfire.multipart": "Integração Multipart", "container.lectern.costs": "Custos:", "container.lectern.manuscript.many": "%s Manuscritos", "container.lectern.no_bestiary": "Não é possível adicionar páginas", "death.attack.dragon.0": "%s foi partido ao meio por um dragão", "death.attack.dragon.1": "%s foi despedaçado por um dragão", "death.attack.dragon.2": "%s foi devorado por um dragão", "death.attack.dragon.attacker_0": "%s foi partido ao meio por %s", "death.attack.dragon.attacker_1": "%s foi despedaçado por %s", "death.attack.dragon.attacker_2": "%s foi devorado por %s", "death.attack.dragon_fire.0": "%s foi transformado em Frango Frito por um dragão", "death.attack.dragon_fire.1": "%s foi incinerado por um dragão", "death.attack.dragon_fire.2": "%s foi transformado em cinzas por um dragão", "death.attack.dragon_fire.attacker_0": "%s foi transformado em Frango Frito por %s", "death.attack.dragon_fire.attacker_1": "%s foi incinerado por %s", "death.attack.dragon_fire.attacker_2": "%s foi transformado em cinzas por %s", "death.attack.dragon_ice.0": "%s foi congelado por um dragão", "death.attack.dragon_ice.1": "%s foi transformado em gelo por um dragão", "death.attack.dragon_ice.2": "%s foi colocado em animação suspensa por um dragão", "death.attack.dragon_ice.attacker_0": "%s foi congelado por %s", "death.attack.dragon_ice.attacker_1": "%s foi transformado em gelo por %s", "death.attack.dragon_ice.attacker_2": "%s foi colocado em animação suspensa por %s", "death.attack.dragon_lightning.0": "%s foi atingido por um raio de um dragão", "death.attack.dragon_lightning.1": "%s foi energizado por um dragão", "death.attack.dragon_lightning.2": "%s foi eletrocutado por um dragão", "death.attack.dragon_lightning.attacker_0": "%s foi energizado por %s", "death.attack.dragon_lightning.attacker_1": "%s foi atingido por um raio de %s", "death.attack.dragon_lightning.attacker_2": "%s foi eletrocutado por %s", "death.attack.gorgon.0": "%s foi transformado em pedra por uma górgona", "death.attack.gorgon.1": "%s foi transformado em gorgonzola por uma górgona", "death.attack.gorgon.2": "%s foi solidificado por uma górgona", "death.attack.gorgon.attacker_0": "%s foi transformado em pedra por %s", "death.attack.gorgon.attacker_1": "%s foi transformado em gorgonzola por %s", "death.attack.gorgon.attacker_2": "%s foi solidificado por %s", "dragon.amethyst": "Ametista", "dragon.armor_body": "Corpo", "dragon.armor_head": "Cabeça", "dragon.armor_neck": "Pescoço", "dragon.armor_tail": "<PERSON><PERSON><PERSON>", "dragon.black": "Preto", "dragon.blue": "Azul", "dragon.bronze": "Bronze", "dragon.command.escort": "Este dragão está te escoltando.", "dragon.command.new_home": "A posição inicial deste dragão foi definida para %d, %d, %d, %s.", "dragon.command.remove_home": "A posição inicial deste dragão foi removida.", "dragon.command.sit": "Este dragão está parado.", "dragon.command.stand": "Este dragão está vagando.", "dragon.copper": "Cobre", "dragon.days.back": "Dias)", "dragon.days.front": "(", "dragon.electric": "<PERSON><PERSON>l Elétrico", "dragon.fire": "Fogo", "dragon.gender": "Gênero:", "dragon.gender.female": "Fêmea", "dragon.gender.male": "<PERSON><PERSON>", "dragon.gray": "Cinza", "dragon.green": "Esm<PERSON><PERSON>", "dragon.hatchtime": "Eclode em:", "dragon.health": "Vida:", "dragon.hunger": "Fome:", "dragon.ice": "<PERSON><PERSON>", "dragon.lightning": "Raio", "dragon.name": "Nome:", "dragon.owner": "Dono:", "dragon.red": "Vermelho", "dragon.sapphire": "Safira", "dragon.silver": "Prata", "dragon.stage": "Estágio", "dragon.unnamed": "Sem nome", "dragon.untamed": "<PERSON><PERSON><PERSON><PERSON>", "dragon.white": "Branco", "dragon_sword_fire.hurt1": "+8 de dano contra Dragõ<PERSON> de Gelo", "dragon_sword_fire.hurt2": "Incendeia e repele alvos", "dragon_sword_ice.hurt1": "+8 de dano contra Drag<PERSON><PERSON> de Fogo", "dragon_sword_ice.hurt2": "<PERSON><PERSON><PERSON>", "dragon_sword_lightning.hurt1": "+4 de dano contra Dragões de Fogo e Dragões de Gelo", "dragon_sword_lightning.hurt2": "Atinge alvos com um raio", "emi.category.iceandfire.fire_forge": "Forja de Dragão de Fogo", "emi.category.iceandfire.ice_forge": "Forja de Dragão de Gelo", "emi.category.iceandfire.lightning_forge": "Forja de Dragão do Raio", "entity.iceandfire.amphithere": "<PERSON><PERSON><PERSON><PERSON>", "entity.iceandfire.amphithere_arrow": "<PERSON><PERSON><PERSON>", "entity.iceandfire.black_frost_dragon": "Gelo Negro", "entity.iceandfire.chain_tie": "<PERSON><PERSON><PERSON>", "entity.iceandfire.cockatrice": "<PERSON><PERSON><PERSON>", "entity.iceandfire.cockatrice_egg": "<PERSON><PERSON> de Cocatrice", "entity.iceandfire.cyclops": "Ciclope", "entity.iceandfire.cylcops_multipart": "<PERSON><PERSON><PERSON> Ciclope", "entity.iceandfire.deathworm": "<PERSON><PERSON><PERSON>", "entity.iceandfire.deathworm_egg": "<PERSON><PERSON> <PERSON> Verme <PERSON> Mo<PERSON>", "entity.iceandfire.dragon_arrow": "Flecha de Osso de Dragão", "entity.iceandfire.dragon_egg": "Ovo <PERSON>", "entity.iceandfire.dragon_multipart": "Parte de Dragão", "entity.iceandfire.dragon_skull": "Crânio de Dragão", "entity.iceandfire.dread_beast": "Besta Sombria", "entity.iceandfire.dread_ghoul": "Carniçal Sombrio", "entity.iceandfire.dread_horse": "Cavalo do Cavaleiro Sombrio", "entity.iceandfire.dread_knight": "Cavaleiro Sombrio", "entity.iceandfire.dread_lich": "<PERSON>ch Sombrio", "entity.iceandfire.dread_lich_skull": "Crânio de Lich Sombrio", "entity.iceandfire.dread_queen": "<PERSON><PERSON>", "entity.iceandfire.dread_scuttler": "<PERSON><PERSON><PERSON><PERSON>", "entity.iceandfire.dread_thrall": "Servo Sombrio", "entity.iceandfire.fire_dragon": "Dragão de Fogo", "entity.iceandfire.fire_dragon_charge": "Carga de Fogo de Dragão", "entity.iceandfire.ghost": "Fantasma", "entity.iceandfire.gorgon": "Górgona", "entity.iceandfire.hippocampus": "Hipocampo", "entity.iceandfire.hippogryph": "Hipogrifo", "entity.iceandfire.hippogryph.": "", "entity.iceandfire.hippogryph.alex": "<PERSON><PERSON><PERSON><PERSON>", "entity.iceandfire.hippogryph.black": "Hipog<PERSON><PERSON>", "entity.iceandfire.hippogryph.brown": "Hipog<PERSON><PERSON>", "entity.iceandfire.hippogryph.chestnut": "<PERSON><PERSON><PERSON><PERSON>", "entity.iceandfire.hippogryph.creamy": "Hipog<PERSON><PERSON>", "entity.iceandfire.hippogryph.dark_brown": "Hipogrifo <PERSON>", "entity.iceandfire.hippogryph.dodo": "<PERSON><PERSON><PERSON><PERSON>", "entity.iceandfire.hippogryph.gray": "<PERSON><PERSON><PERSON><PERSON>", "entity.iceandfire.hippogryph.raptor": "<PERSON><PERSON><PERSON><PERSON>", "entity.iceandfire.hippogryph.white": "Hipogrifo <PERSON>", "entity.iceandfire.hippogryph_egg": "<PERSON><PERSON> de Hipogrifo", "entity.iceandfire.hydra": "<PERSON><PERSON>", "entity.iceandfire.hydra_arrow": "<PERSON><PERSON><PERSON>", "entity.iceandfire.hydra_breath": "<PERSON><PERSON><PERSON> Hidra", "entity.iceandfire.hydra_multipart": "Pescoço de Hidra", "entity.iceandfire.ice_dragon": "Dragão de Gelo", "entity.iceandfire.ice_dragon_charge": "Carga de Gelo de Dragão", "entity.iceandfire.lightning_dragon": "Dragão do Raio", "entity.iceandfire.lightning_dragon_charge": "Carga de Raio de Dragão", "entity.iceandfire.mob_skull": "Crânio", "entity.iceandfire.multipart": "Parte", "entity.iceandfire.pixie": "<PERSON><PERSON><PERSON>", "entity.iceandfire.pixie.type_0": "<PERSON><PERSON><PERSON>", "entity.iceandfire.pixie.type_1": "<PERSON><PERSON>e <PERSON>", "entity.iceandfire.pixie.type_2": "<PERSON><PERSON><PERSON>", "entity.iceandfire.pixie.type_3": "Pixie Verde", "entity.iceandfire.pixie.type_4": "<PERSON><PERSON><PERSON>", "entity.iceandfire.pixie_charge": "Raio da Varinha de Pixie", "entity.iceandfire.player": "Jogador", "entity.iceandfire.sea_serpent": "<PERSON><PERSON><PERSON>", "entity.iceandfire.sea_serpent_arrow": "<PERSON><PERSON><PERSON>", "entity.iceandfire.sea_serpent_bubbles": "<PERSON><PERSON><PERSON>", "entity.iceandfire.siren": "Serei<PERSON>", "entity.iceandfire.stone_statue": "Estátua de Pedra", "entity.iceandfire.stymphalian_arrow": "<PERSON><PERSON><PERSON>", "entity.iceandfire.stymphalian_bird": "<PERSON><PERSON><PERSON><PERSON>", "entity.iceandfire.stymphalian_feather": "Pena de Pássaro <PERSON>", "entity.iceandfire.tide_trident": "Tridente da Maré", "entity.iceandfire.troll": "Troll", "entity.minecraft.villager.iceandfire.scribe": "Escriba", "entity.minecraft.villager.scribe": "Escriba", "fluid.tconstruct.dragonsteel_fire": "Aço de Dragão de Fogo Derretido", "fluid.tconstruct.dragonsteel_ice": "Aço de Dragão de Gelo Derretido", "hippogryph.command.new_home": "A posição inicial deste hipogrifo foi definida para %d, %d, %d.", "hippogryph.command.remove_home": "A posição inicial deste hipogrifo foi removida.", "hippogryph.command.sit": "Este hipogrifo está parado.", "hippogryph.command.stand": "Este hipogrifo está vagando.", "iceandfire.amphithere.attackDamage": "Dano de Ataque", "iceandfire.amphithere.flightSpeed": "Velocidade de Voo", "iceandfire.amphithere.maxHealth": "Vida Máxima", "iceandfire.amphithere.spawn": "<PERSON><PERSON>", "iceandfire.amphithere.spawnWeight": "Peso de Nascimento", "iceandfire.amphithere.tameTime": "Tempo de Domesticação", "iceandfire.amphithere.villagerSearchLength": "Alcance de Busca por Aldeões", "iceandfire.armors.dragonSteelBaseArmor": "Armadura Base", "iceandfire.armors.dragonSteelBaseDamage": "Dano Base", "iceandfire.armors.dragonSteelBaseDurability": "Durabilidade Base", "iceandfire.armors.dragonsteelArmorEnchantability": "Encantabilidade da Armadura de Aço de Dragão", "iceandfire.armors.dragonsteelArmorKnockbackResistance": "Resistência a Repulsão da Armadura de Aço de Dragão", "iceandfire.armors.dragonsteelArmorToughness": "Resistência da Armadura de Aço de Dragão", "iceandfire.armors.dragonsteelBootsArmor": "Armadura das Botas de Aço de Dragão", "iceandfire.armors.dragonsteelBootsDurability": "Durabilidade das Botas de Aço de Dragão", "iceandfire.armors.dragonsteelChestplateArmor": "Armadura do Peitoral de Aço de Dragão", "iceandfire.armors.dragonsteelChestplateDurability": "Durabilidade do Peitoral de Aço de Dragão", "iceandfire.armors.dragonsteelHelmetArmor": "Armadura do Elmo de Aço de Dragão", "iceandfire.armors.dragonsteelHelmetDurability": "Durabilidade do Elmo de Aço de Dragão", "iceandfire.armors.dragonsteelLeggingsArmor": "Armadura das Calças de Aço de Dragão", "iceandfire.armors.dragonsteelLeggingsDurability": "Durabilidade das Calças de Aço de Dragão", "iceandfire.bird.attackAnimals": "<PERSON><PERSON><PERSON>", "iceandfire.bird.dataTagDrops": "Drops de Data Tag", "iceandfire.bird.dataTagDrops.comment": "Não utilizado", "iceandfire.bird.featherAttackDamage": "Dano de Ataque da Pena", "iceandfire.bird.featherDropChance": "Chance de Drop de Pena", "iceandfire.bird.flightHeight": "Altura de Voo", "iceandfire.bird.flockLength": "Tamanho do Bando", "iceandfire.bird.spawnChance": "Chance de Nascimento", "iceandfire.bird.targetSearchLength": "Alcance de Busca de Alvo", "iceandfire.book.landing": "Este é um texto de aterrissagem.", "iceandfire.book.name": "<PERSON><PERSON><PERSON><PERSON>", "iceandfire.category.amphithere": "<PERSON><PERSON><PERSON><PERSON>", "iceandfire.category.armors": "Armad<PERSON><PERSON>", "iceandfire.category.bird": "<PERSON><PERSON><PERSON><PERSON>", "iceandfire.category.cockatrice": "<PERSON><PERSON><PERSON>", "iceandfire.category.cyclops": "Ciclope", "iceandfire.category.deathworm": "<PERSON><PERSON><PERSON>", "iceandfire.category.dragon": "Dragão", "iceandfire.category.ghost": "Fantasma", "iceandfire.category.gorgon": "Górgona", "iceandfire.category.hippocampus": "Hipocampo", "iceandfire.category.hippogryphs": "Hipogri<PERSON>s", "iceandfire.category.hydra": "<PERSON><PERSON>", "iceandfire.category.lich": "Lich", "iceandfire.category.misc": "Diversos", "iceandfire.category.pixie": "<PERSON><PERSON><PERSON>", "iceandfire.category.seaSerpent": "<PERSON><PERSON><PERSON>", "iceandfire.category.siren": "Serei<PERSON>", "iceandfire.category.tools": "Ferramentas", "iceandfire.category.troll": "Troll", "iceandfire.category.worldgen": "Geração de Mundo", "iceandfire.client": "Cliente", "iceandfire.cockatrice.chickenSearchLength": "Alcance de Busca por Galinhas", "iceandfire.cockatrice.chickensLayRottenEggs": "Galinhas Botam Ovos Podres", "iceandfire.cockatrice.eggChance": "Chance de Nascer ao Jo<PERSON>", "iceandfire.cockatrice.maxHealth": "Vida Máxima", "iceandfire.cockatrice.spawn": "<PERSON><PERSON>", "iceandfire.cockatrice.spawnWeight": "Peso de Nascimento", "iceandfire.customMainMenu": "<PERSON>u Principal <PERSON>", "iceandfire.cyclops.attackDamage": "Dano de Ataque", "iceandfire.cyclops.biteDamage": "<PERSON><PERSON>", "iceandfire.cyclops.griefing": "Griefing", "iceandfire.cyclops.maxHealth": "Vida Máxima", "iceandfire.cyclops.sheepSearchLength": "Alcance de Busca por Ovelhas", "iceandfire.cyclops.spawnCaveChance": "Chance de Nascimento em Cavernas", "iceandfire.cyclops.spawnWanderingChance": "Chance de Nascimento Vagando", "iceandfire.deathworm.attackDamage": "Dano de Ataque", "iceandfire.deathworm.attackMonsters": "Atacar <PERSON>", "iceandfire.deathworm.maxHealth": "Vida Máxima", "iceandfire.deathworm.spawnChance": "Chance de Nascimento", "iceandfire.deathworm.targetSearchLength": "Alcance de Busca de Alvo", "iceandfire.dragon.animalsFear": "<PERSON><PERSON><PERSON>", "iceandfire.dragon.attackDamage": "Dano de Ataque", "iceandfire.dragon.attackDamageFire": "Dano de Ataque de Fogo", "iceandfire.dragon.attackDamageIce": "Dano de Ataque de Gelo", "iceandfire.dragon.attackDamageLightning": "Dano de Ataque de Raio", "iceandfire.dragon.blockBreakingDropChance": "Chance de Drop ao Quebrar <PERSON>", "iceandfire.dragon.breakBlockCooldown": "Tempo de Recarga para Quebrar Blocos", "iceandfire.dragon.brushTimesMul": "Multiplicador de Vezes Escovado", "iceandfire.dragon.canDespawn": "<PERSON><PERSON>", "iceandfire.dragon.canHealFromBiting": "Pode se Curar ao Morder", "iceandfire.dragon.chunkLoadSummonCrystal": "Carregar Chunk do Cristal de Invocação", "iceandfire.dragon.digWhenStuck": "<PERSON><PERSON><PERSON>", "iceandfire.dragon.dragonFlightSpeedMod": "Modificador de Velocidade de Voo do Dragão", "iceandfire.dragon.eggBornTime": "Tempo de Eclosão do Ovo", "iceandfire.dragon.enableBrushDragonScales": "Habilitar Escovação de Escamas de Dragão", "iceandfire.dragon.explosiveBreath": "Sopro Explosivo", "iceandfire.dragon.flapNoiseDistance": "Distância do Barulho de Bater Asas", "iceandfire.dragon.fluteDistance": "Distância de Efeito da Flauta", "iceandfire.dragon.generate.denGoldAmount": "Quantidade de Ouro na Toca", "iceandfire.dragon.generate.oreRatio": "Taxa de Minério", "iceandfire.dragon.generate.skeletonChance": "Chance de Esqueleto", "iceandfire.dragon.generate.skeletons": "<PERSON><PERSON><PERSON>", "iceandfire.dragon.goldSearchLength": "Distância de Busca por Ouro", "iceandfire.dragon.griefing": "Griefing", "iceandfire.dragon.hungerTickRate": "Taxa de Tique de Fome", "iceandfire.dragon.loot.blood": "<PERSON><PERSON>", "iceandfire.dragon.loot.heart": "Loot de Coração", "iceandfire.dragon.loot.skull": "Loot de Crânio", "iceandfire.dragon.maxBreathTimeMul": "Multiplicador de Tempo Máximo de Sopro", "iceandfire.dragon.maxBrushScalesDropPerTime": "Máximo de Escamas por Escovação", "iceandfire.dragon.maxFlight": "Altura Máxima de Voo", "iceandfire.dragon.maxHealth": "Vida Máxima", "iceandfire.dragon.maxPathingNodes": "Máximo de Nós <PERSON>", "iceandfire.dragon.maxTamedDragonAge": "Idade Máxima do Dragão Domado (Dias)", "iceandfire.dragon.movedWronglyFix": "Correção de Movimento Incorreto", "iceandfire.dragon.movedWronglyFix.comment": "Não utilizado", "iceandfire.dragon.neutralToPlayer": "Padrão Neutro ao Jogador", "iceandfire.dragon.sleep": "Pode Dormir", "iceandfire.dragon.tamedGriefing": "<PERSON>rief<PERSON>", "iceandfire.dragon.targetSearchLength": "Distância de Busca de Alvo", "iceandfire.dragon.villagersFear": "Aldeões Têm <PERSON>", "iceandfire.dragon.wanderFromHomeDistance": "Distância de Vagueio de Casa", "iceandfire.dragonAuto3rdPerson": "Perspectiva Automática em 3ª Pessoa", "iceandfire.empty": "<PERSON><PERSON><PERSON>", "iceandfire.fire_dragon_forge": "Forja de Dragão de Fogo", "iceandfire.ghost.attackDamage": "Dano de Ataque", "iceandfire.ghost.fromPlayerDeaths": "De Mortes de Jogadores", "iceandfire.ghost.generateGraveyards": "<PERSON><PERSON><PERSON>", "iceandfire.ghost.maxHealth": "Vida Máxima", "iceandfire.gorgon.maxHealth": "Vida Máxima", "iceandfire.hippocampus.spawnChance": "Chance de Nascimento de Hipocampo", "iceandfire.hippocampus.swimSpeedMod": "Modificador de Velocidade de Nado do Hipocampo", "iceandfire.hippogryphs.fightSpeedMod": "Modificador de Velocidade de Luta", "iceandfire.hippogryphs.spawn": "<PERSON><PERSON>", "iceandfire.hippogryphs.spawnWeight": "Peso de Nascimento", "iceandfire.hydra.maxHealth": "Vida Máxima", "iceandfire.hydra.spawnChance": "Chance de Nascimento", "iceandfire.ice_dragon_forge": "Forja de Dragão de Gelo", "iceandfire.lich.spawn": "<PERSON><PERSON>", "iceandfire.lich.spawnChance": "Chance de Nascimento", "iceandfire.lich.spawnWeight": "Peso de Nascimento", "iceandfire.lightning_dragon_forge": "Forja de Dragão do Raio", "iceandfire.misc.allowAttributeOverriding": "Permitir Substituição de Atributos", "iceandfire.misc.allowAttributeOverriding.comment": "Não utilizado", "iceandfire.misc.dreadQueenMaxHealth": "Vida Máxima da Rainha Sombria", "iceandfire.misc.enableDragonSeeker": "Habilitar Buscador de Dragões", "iceandfire.pixie.size": "<PERSON><PERSON><PERSON>", "iceandfire.pixie.spawnChance": "Chance de Nascimento", "iceandfire.pixie.stealItems": "<PERSON><PERSON><PERSON>", "iceandfire.seaSerpent.attackDamage": "Força de Ataque", "iceandfire.seaSerpent.baseHealth": "Vida Base", "iceandfire.seaSerpent.griefing": "Griefing", "iceandfire.seaSerpent.spawnChance": "Chance de Nascimento", "iceandfire.siren.maxHealth": "Vida Máxima", "iceandfire.siren.maxSingTime": "Tempo Máximo de Canto", "iceandfire.siren.shader": "Shader", "iceandfire.siren.spawnChance": "Chance de Nascimento", "iceandfire.siren.timeBetweenSongs": "Tempo Entre Canções", "iceandfire.sound.subtitle.amphithere_bite": "<PERSON><PERSON><PERSON><PERSON> morde", "iceandfire.sound.subtitle.amphithere_die": "<PERSON><PERSON><PERSON><PERSON> morre", "iceandfire.sound.subtitle.amphithere_hurt": "Anfit<PERSON><PERSON> se machuca", "iceandfire.sound.subtitle.amphithere_idle": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>na", "iceandfire.sound.subtitle.bestiary_page": "Página do bestiário vira", "iceandfire.sound.subtitle.cockatrice_cry": "<PERSON><PERSON><PERSON> chora", "iceandfire.sound.subtitle.cockatrice_die": "<PERSON><PERSON><PERSON> morre", "iceandfire.sound.subtitle.cockatrice_hurt": "Cocatrice se machuca", "iceandfire.sound.subtitle.cockatrice_idle": "<PERSON><PERSON><PERSON> grasna", "iceandfire.sound.subtitle.cyclops_bite": "Ciclope morde", "iceandfire.sound.subtitle.cyclops_blinded": "Ciclope berra de dor", "iceandfire.sound.subtitle.cyclops_die": "Ciclope morre", "iceandfire.sound.subtitle.cyclops_hurt": "Ciclope se machuca", "iceandfire.sound.subtitle.cyclops_idle": "Ciclope resmunga", "iceandfire.sound.subtitle.deathworm_attack": "<PERSON><PERSON><PERSON> da morte ataca", "iceandfire.sound.subtitle.deathworm_die": "<PERSON>erme da morte morre", "iceandfire.sound.subtitle.deathworm_hurt": "<PERSON>erme da morte se machuca", "iceandfire.sound.subtitle.deathworm_idle": "<PERSON><PERSON><PERSON> da morte berra", "iceandfire.sound.subtitle.dragon_hatch": "Dragão eclode", "iceandfire.sound.subtitle.dragonegg_hatch": "Ovo de dragão eclode", "iceandfire.sound.subtitle.dragonflute": "Flauta de dragão toca", "iceandfire.sound.subtitle.dread_ghoul_idle": "Carniçal Sombrio rosna", "iceandfire.sound.subtitle.dread_lich_summon": "Lich Sombrio invoca", "iceandfire.sound.subtitle.firedragon_breath": "Dragão de Fogo solta fogo", "iceandfire.sound.subtitle.firedragon_death": "Dragão de Fogo morre", "iceandfire.sound.subtitle.firedragon_hurt": "Dragão de Fogo se machuca", "iceandfire.sound.subtitle.firedragon_idle": "Dragão de Fogo rosna", "iceandfire.sound.subtitle.firedragon_roar": "Dragão de Fogo ruge", "iceandfire.sound.subtitle.ghost_attack": "Fantasma ataca", "iceandfire.sound.subtitle.ghost_die": "Fantas<PERSON> morre", "iceandfire.sound.subtitle.ghost_hurt": "Fantasma se machuca", "iceandfire.sound.subtitle.ghost_idle": "Fantasma exala", "iceandfire.sound.subtitle.gold_pile_break": "<PERSON><PERSON> se espalham", "iceandfire.sound.subtitle.gold_pile_step": "<PERSON><PERSON>", "iceandfire.sound.subtitle.gorgon_attack": "Górgona ataca", "iceandfire.sound.subtitle.gorgon_die": "Górgona morre", "iceandfire.sound.subtitle.gorgon_hurt": "Górgona se machuca", "iceandfire.sound.subtitle.gorgon_idle": "<PERSON><PERSON><PERSON><PERSON> gar<PERSON>", "iceandfire.sound.subtitle.gorgon_petrify": "Górgona grita", "iceandfire.sound.subtitle.gorgon_turn_stone": "Algo vira pedra", "iceandfire.sound.subtitle.hippocampus_die": "Hipocampo morre", "iceandfire.sound.subtitle.hippocampus_hurt": "Hipocampo se machuca", "iceandfire.sound.subtitle.hippocampus_idle": "Hipocampo resmunga", "iceandfire.sound.subtitle.hippogryph_die": "Hipogrifo morre", "iceandfire.sound.subtitle.hippogryph_hurt": "Hipogrifo se <PERSON>hu<PERSON>", "iceandfire.sound.subtitle.hippogryph_idle": "<PERSON><PERSON><PERSON><PERSON>", "iceandfire.sound.subtitle.hydra_die": "<PERSON><PERSON> morre", "iceandfire.sound.subtitle.hydra_hurt": "Hidra se machuca", "iceandfire.sound.subtitle.hydra_idle": "<PERSON><PERSON> sibila", "iceandfire.sound.subtitle.hydra_spit": "<PERSON><PERSON> cospe", "iceandfire.sound.subtitle.icedragon_breath": "Dragão de Gelo solta gelo", "iceandfire.sound.subtitle.icedragon_death": "Dragão de Gelo morre", "iceandfire.sound.subtitle.icedragon_hurt": "Dragão de Gelo se machuca", "iceandfire.sound.subtitle.icedragon_idle": "Dragão de Gelo rosna", "iceandfire.sound.subtitle.icedragon_roar": "Dragão de Gelo ruge", "iceandfire.sound.subtitle.lightningdragon_breath": "Dragão do Raio solta raio", "iceandfire.sound.subtitle.lightningdragon_death": "Dragão do Raio morre", "iceandfire.sound.subtitle.lightningdragon_hurt": "Dragão do Raio se machuca", "iceandfire.sound.subtitle.lightningdragon_idle": "Dragão do Raio rosna", "iceandfire.sound.subtitle.lightningdragon_roar": "Dragão do Raio ruge", "iceandfire.sound.subtitle.mermaid_die": "<PERSON><PERSON><PERSON> morre", "iceandfire.sound.subtitle.mermaid_hurt": "Sereia se machuca", "iceandfire.sound.subtitle.mermaid_idle": "Serei<PERSON> brinca", "iceandfire.sound.subtitle.naga_attack": "Naga ataca", "iceandfire.sound.subtitle.naga_die": "Naga morre", "iceandfire.sound.subtitle.naga_hurt": "Naga se machuca", "iceandfire.sound.subtitle.naga_idle": "Naga geme", "iceandfire.sound.subtitle.pixie_die": "<PERSON><PERSON>e morre", "iceandfire.sound.subtitle.pixie_hurt": "Pixie se machuca", "iceandfire.sound.subtitle.pixie_idle": "<PERSON><PERSON>e dá risadinhas", "iceandfire.sound.subtitle.pixie_taunt": "<PERSON>xie provoca", "iceandfire.sound.subtitle.pixie_wand": "Varinha de pixie lança feitiço", "iceandfire.sound.subtitle.sea_serpent_bite": "<PERSON><PERSON>e marinha morde", "iceandfire.sound.subtitle.sea_serpent_breath": "<PERSON><PERSON><PERSON> marinha atira <PERSON>", "iceandfire.sound.subtitle.sea_serpent_die": "<PERSON><PERSON>e marinha morre", "iceandfire.sound.subtitle.sea_serpent_hurt": "<PERSON><PERSON>e marinha se machuca", "iceandfire.sound.subtitle.sea_serpent_idle": "<PERSON><PERSON><PERSON> marinha sibila", "iceandfire.sound.subtitle.sea_serpent_roar": "<PERSON><PERSON>e marinha ruge", "iceandfire.sound.subtitle.siren_song": "Canção da sereia", "iceandfire.sound.subtitle.stymphalian_bird_attack": "<PERSON><PERSON><PERSON><PERSON>", "iceandfire.sound.subtitle.stymphalian_bird_die": "<PERSON><PERSON><PERSON><PERSON> est<PERSON><PERSON> morre", "iceandfire.sound.subtitle.stymphalian_bird_hurt": "<PERSON>ás<PERSON><PERSON> est<PERSON> se mac<PERSON>", "iceandfire.sound.subtitle.stymphalian_bird_idle": "<PERSON><PERSON><PERSON><PERSON><PERSON> ch<PERSON>", "iceandfire.sound.subtitle.troll_die": "Troll morre", "iceandfire.sound.subtitle.troll_hurt": "Troll se machuca", "iceandfire.sound.subtitle.troll_idle": "Troll rosna", "iceandfire.sound.subtitle.troll_roar": "Troll ruge", "iceandfire.tools.dragonBloodFireDuration": "Duração do Fogo do Sangue de Dragão", "iceandfire.tools.dragonBloodFrozenDuration": "Duração do Congelamento do Sangue de Dragão", "iceandfire.tools.dragonFireAbility": "Habilidades Relacionadas ao Dragão de Fogo", "iceandfire.tools.dragonIceAbility": "Habilidades Relacionadas ao Dragão de Gelo", "iceandfire.tools.dragonLightningAbility": "Habilidades Relacionadas ao Dragão do Raio", "iceandfire.tools.dragonsteelFireDuration": "Duração do Fogo do Aço de Dragão", "iceandfire.tools.dragonsteelFrozenDuration": "Duração do Congelamento do Aço de Dragão", "iceandfire.tools.phantasmalBladeAbility": "Habilidade da Lâmina Fantasmagórica", "iceandfire.troll.attackDamage": "Dano de Ataque", "iceandfire.troll.dropWeapon": "Dropar Arma", "iceandfire.troll.maxHealth": "Vida Máxima", "iceandfire.troll.spawn": "<PERSON><PERSON>", "iceandfire.troll.spawnWeight": "Peso de Nascimento", "iceandfire.worldgen.dangerousDistanceLimit": "Limite de Distância Perigosa", "iceandfire.worldgen.dangerousSeparationLimit": "Limite de Separação Perigosa", "iceandfire.worldgen.generateCyclopsCaveChance": "Chance de Gerar Caverna de Ciclope", "iceandfire.worldgen.generateFireDragonCaveChance": "Chance de Gerar Caverna de Dragão de Fogo", "iceandfire.worldgen.generateFireDragonRoostChance": "Chance de Gerar Ninho de Dragão de Fogo", "iceandfire.worldgen.generateGorgonTempleChance": "Chance de Gerar Templo de Górgona", "iceandfire.worldgen.generateGraveYardChance": "Chance de Gerar <PERSON>", "iceandfire.worldgen.generateHydraCaveChance": "Chance de Gerar <PERSON>", "iceandfire.worldgen.generateIceDragonCaveChance": "Chance de Gerar Caverna de Dragão de Gelo", "iceandfire.worldgen.generateIceDragonRoostChance": "Chance de Gerar Ninho de Dragão de Gelo", "iceandfire.worldgen.generateLightningDragonCaveChance": "Chance de Gerar Caverna de Dragão do Raio", "iceandfire.worldgen.generateLightningDragonRoostChance": "Chance de Gerar Ninho de Dragão do Raio", "iceandfire.worldgen.generateMausoleumChance": "Chance de Gerar <PERSON>", "iceandfire.worldgen.generateMyemexHiveDesertChance": "Chance de Gerar Colmeia Myrmex do Deserto", "iceandfire.worldgen.generateMyemexHiveJungleChance": "Chance de Gerar Colmeia Myrmex da Selva", "iceandfire.worldgen.generatePixieVillageChance": "Chance de Gerar Vila de Pixies", "iceandfire.worldgen.generateSirenIslandChance": "Chance de Gerar <PERSON>", "iceandfire.worldgen.villagerHouseWeight": "Peso da Casa do Aldeão", "iceandfire.worldgen.villagerHouseWeight.comment": "Não utilizado", "item.dragonscales_armor.desc": "Proteção aumentada contra ataques de sopro de dragão", "item.iceandfire.air_pods": "Apple AirPods", "item.iceandfire.air_pods.desc": "Oh meu Deus, o <PERSON> está com AirPods, ele não pode nos ouvir!", "item.iceandfire.ambrosia": "Ambrosia", "item.iceandfire.amethyst_gem": "Ametista", "item.iceandfire.amphithere_arrow": "<PERSON><PERSON><PERSON>", "item.iceandfire.amphithere_arrow.desc": "Repele todos os mobs com o poder das asas de um anfitérion!", "item.iceandfire.amphithere_feather": "<PERSON><PERSON>", "item.iceandfire.amphithere_macuahuitl": "Macuahuitl de Anfitérion", "item.iceandfire.amphithere_macuahuitl.desc_0": "Joga entidades para cima e para trás", "item.iceandfire.amphithere_macuahuitl.desc_1": "Desativa escudos", "item.iceandfire.amphithere_skull": "<PERSON><PERSON><PERSON><PERSON>", "item.iceandfire.amphithere_skull.jei_desc": "Pode ser colocado no chão ou em paredes como um troféu.", "item.iceandfire.armor_copper_metal_boots": "Botas de Cobre", "item.iceandfire.armor_copper_metal_chestplate": "<PERSON><PERSON><PERSON>", "item.iceandfire.armor_copper_metal_helmet": "<PERSON><PERSON>", "item.iceandfire.armor_copper_metal_leggings": "Calças de Cobre", "item.iceandfire.armor_silver_metal_boots": "Botas de Prata", "item.iceandfire.armor_silver_metal_chestplate": "<PERSON><PERSON><PERSON>", "item.iceandfire.armor_silver_metal_helmet": "<PERSON><PERSON>", "item.iceandfire.armor_silver_metal_leggings": "Calças de Prata", "item.iceandfire.banner_pattern_amphithere": "Padrão de Estandarte", "item.iceandfire.banner_pattern_amphithere.desc": "<PERSON><PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_amphithere.desc.black": "<PERSON><PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_amphithere.desc.blue": "<PERSON><PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_amphithere.desc.brown": "<PERSON><PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_amphithere.desc.cyan": "<PERSON><PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_amphithere.desc.gray": "<PERSON><PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_amphithere.desc.green": "Anfitérion Verde", "item.iceandfire.banner_pattern_amphithere.desc.light_blue": "<PERSON><PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_amphithere.desc.light_gray": "<PERSON><PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_amphithere.desc.lime": "<PERSON><PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_amphithere.desc.magenta": "<PERSON><PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_amphithere.desc.orange": "<PERSON><PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_amphithere.desc.pink": "<PERSON><PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_amphithere.desc.purple": "<PERSON><PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_amphithere.desc.red": "<PERSON><PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_amphithere.desc.white": "<PERSON><PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_amphithere.desc.yellow": "<PERSON><PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_bird": "Padrão de Estandarte", "item.iceandfire.banner_pattern_bird.desc": "<PERSON><PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_bird.desc.black": "<PERSON>ás<PERSON><PERSON>", "item.iceandfire.banner_pattern_bird.desc.blue": "<PERSON><PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_bird.desc.brown": "<PERSON><PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_bird.desc.cyan": "<PERSON><PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_bird.desc.gray": "<PERSON><PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_bird.desc.green": "Pássaro Verde", "item.iceandfire.banner_pattern_bird.desc.light_blue": "Pássaro <PERSON>", "item.iceandfire.banner_pattern_bird.desc.light_gray": "Pássaro <PERSON>lar<PERSON>", "item.iceandfire.banner_pattern_bird.desc.lime": "Pássaro <PERSON>", "item.iceandfire.banner_pattern_bird.desc.magenta": "<PERSON><PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_bird.desc.orange": "<PERSON><PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_bird.desc.pink": "<PERSON><PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_bird.desc.purple": "Pássar<PERSON>", "item.iceandfire.banner_pattern_bird.desc.red": "<PERSON><PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_bird.desc.white": "Pássaro Bran<PERSON>", "item.iceandfire.banner_pattern_bird.desc.yellow": "<PERSON><PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_dread": "Padrão de Estandarte", "item.iceandfire.banner_pattern_dread.desc": "Sombrio", "item.iceandfire.banner_pattern_dread.desc.black": "Sombrio <PERSON>", "item.iceandfire.banner_pattern_dread.desc.blue": "Sombrio Azul", "item.iceandfire.banner_pattern_dread.desc.brown": "<PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_dread.desc.cyan": "<PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_dread.desc.gray": "So<PERSON><PERSON>", "item.iceandfire.banner_pattern_dread.desc.green": "Sombrio Verde", "item.iceandfire.banner_pattern_dread.desc.light_blue": "Sombrio Azul Claro", "item.iceandfire.banner_pattern_dread.desc.light_gray": "Sombrio Cinza Claro", "item.iceandfire.banner_pattern_dread.desc.lime": "Sombrio Lima", "item.iceandfire.banner_pattern_dread.desc.magenta": "<PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_dread.desc.orange": "<PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_dread.desc.pink": "<PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_dread.desc.purple": "Sombrio R<PERSON>", "item.iceandfire.banner_pattern_dread.desc.red": "<PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_dread.desc.white": "Sombrio Branco", "item.iceandfire.banner_pattern_dread.desc.yellow": "<PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_eye": "Padrão de Estandarte", "item.iceandfire.banner_pattern_eye.desc": "<PERSON><PERSON><PERSON> Ciclope", "item.iceandfire.banner_pattern_eye.desc.black": "<PERSON><PERSON><PERSON> de Ciclope Preto", "item.iceandfire.banner_pattern_eye.desc.blue": "O<PERSON>ho de Ciclope Azul", "item.iceandfire.banner_pattern_eye.desc.brown": "<PERSON><PERSON><PERSON> de Ciclope Marrom", "item.iceandfire.banner_pattern_eye.desc.cyan": "<PERSON><PERSON><PERSON> de Ciclope Ciano", "item.iceandfire.banner_pattern_eye.desc.gray": "<PERSON><PERSON><PERSON> de Ciclope Cinza", "item.iceandfire.banner_pattern_eye.desc.green": "Olho de Ciclope Verde", "item.iceandfire.banner_pattern_eye.desc.light_blue": "O<PERSON>ho de Ciclope Azul Claro", "item.iceandfire.banner_pattern_eye.desc.light_gray": "<PERSON><PERSON><PERSON> de Ciclope Cinza Claro", "item.iceandfire.banner_pattern_eye.desc.lime": "O<PERSON>ho de Ciclope Lima", "item.iceandfire.banner_pattern_eye.desc.magenta": "<PERSON><PERSON><PERSON> de Ciclope Magenta", "item.iceandfire.banner_pattern_eye.desc.orange": "<PERSON><PERSON><PERSON> de Ciclope Laranja", "item.iceandfire.banner_pattern_eye.desc.pink": "<PERSON><PERSON><PERSON> de Ciclope Rosa", "item.iceandfire.banner_pattern_eye.desc.purple": "O<PERSON>ho de Ciclope Roxo", "item.iceandfire.banner_pattern_eye.desc.red": "<PERSON><PERSON><PERSON> de Ciclope Vermelho", "item.iceandfire.banner_pattern_eye.desc.white": "<PERSON><PERSON><PERSON> de Ciclope Branco", "item.iceandfire.banner_pattern_eye.desc.yellow": "<PERSON><PERSON><PERSON> de Ciclope Amarelo", "item.iceandfire.banner_pattern_fae": "Padrão de Estandarte", "item.iceandfire.banner_pattern_fae.desc": "<PERSON><PERSON>", "item.iceandfire.banner_pattern_fae.desc.black": "Fae Preta", "item.iceandfire.banner_pattern_fae.desc.blue": "<PERSON>ae <PERSON>", "item.iceandfire.banner_pattern_fae.desc.brown": "<PERSON><PERSON>", "item.iceandfire.banner_pattern_fae.desc.cyan": "<PERSON>ae <PERSON>", "item.iceandfire.banner_pattern_fae.desc.gray": "<PERSON><PERSON>", "item.iceandfire.banner_pattern_fae.desc.green": "Fae Verde", "item.iceandfire.banner_pattern_fae.desc.light_blue": "Fae Azul <PERSON>", "item.iceandfire.banner_pattern_fae.desc.light_gray": "<PERSON>ae <PERSON>", "item.iceandfire.banner_pattern_fae.desc.lime": "Fae Lima", "item.iceandfire.banner_pattern_fae.desc.magenta": "<PERSON>ae <PERSON>a", "item.iceandfire.banner_pattern_fae.desc.orange": "<PERSON><PERSON>", "item.iceandfire.banner_pattern_fae.desc.pink": "<PERSON><PERSON>", "item.iceandfire.banner_pattern_fae.desc.purple": "<PERSON>ae R<PERSON>a", "item.iceandfire.banner_pattern_fae.desc.red": "<PERSON><PERSON>", "item.iceandfire.banner_pattern_fae.desc.white": "Fae Branca", "item.iceandfire.banner_pattern_fae.desc.yellow": "<PERSON><PERSON>", "item.iceandfire.banner_pattern_feather": "Padrão de Estandarte", "item.iceandfire.banner_pattern_feather.desc": "<PERSON><PERSON>", "item.iceandfire.banner_pattern_feather.desc.black": "Pena Preta", "item.iceandfire.banner_pattern_feather.desc.blue": "Pena <PERSON>", "item.iceandfire.banner_pattern_feather.desc.brown": "<PERSON><PERSON>", "item.iceandfire.banner_pattern_feather.desc.cyan": "Pena Ciano", "item.iceandfire.banner_pattern_feather.desc.gray": "<PERSON><PERSON>", "item.iceandfire.banner_pattern_feather.desc.green": "Pena Verde", "item.iceandfire.banner_pattern_feather.desc.light_blue": "Pena Azul Claro", "item.iceandfire.banner_pattern_feather.desc.light_gray": "Pena Cinza Claro", "item.iceandfire.banner_pattern_feather.desc.lime": "Pena Lima", "item.iceandfire.banner_pattern_feather.desc.magenta": "<PERSON><PERSON>", "item.iceandfire.banner_pattern_feather.desc.orange": "<PERSON><PERSON>", "item.iceandfire.banner_pattern_feather.desc.pink": "<PERSON><PERSON>", "item.iceandfire.banner_pattern_feather.desc.purple": "<PERSON><PERSON>", "item.iceandfire.banner_pattern_feather.desc.red": "<PERSON><PERSON>", "item.iceandfire.banner_pattern_feather.desc.white": "Pena Branca", "item.iceandfire.banner_pattern_feather.desc.yellow": "<PERSON><PERSON>", "item.iceandfire.banner_pattern_fire": "Padrão de Estandarte", "item.iceandfire.banner_pattern_fire.desc": "Dragão de Fogo", "item.iceandfire.banner_pattern_fire.desc.black": "Dragão de Fogo Preto", "item.iceandfire.banner_pattern_fire.desc.blue": "Dragão de Fogo Azul", "item.iceandfire.banner_pattern_fire.desc.brown": "Dragão de Fogo Marrom", "item.iceandfire.banner_pattern_fire.desc.cyan": "Dragão de Fogo Ciano", "item.iceandfire.banner_pattern_fire.desc.gray": "Dragão de Fogo Cinza", "item.iceandfire.banner_pattern_fire.desc.green": "Dragão de Fogo Verde", "item.iceandfire.banner_pattern_fire.desc.light_blue": "Dragão de Fogo Azul Claro", "item.iceandfire.banner_pattern_fire.desc.light_gray": "Dragão de Fogo Cinza Claro", "item.iceandfire.banner_pattern_fire.desc.lime": "Dragão de Fogo Lima", "item.iceandfire.banner_pattern_fire.desc.magenta": "Dragão de Fogo Magenta", "item.iceandfire.banner_pattern_fire.desc.orange": "Dragão de Fogo Laranja", "item.iceandfire.banner_pattern_fire.desc.pink": "Dragão de Fogo Rosa", "item.iceandfire.banner_pattern_fire.desc.purple": "Dragão de Fogo Roxo", "item.iceandfire.banner_pattern_fire.desc.red": "Dragão de Fogo Vermelho", "item.iceandfire.banner_pattern_fire.desc.white": "Dragão de Fogo Branco", "item.iceandfire.banner_pattern_fire.desc.yellow": "Dragão de Fogo Amarelo", "item.iceandfire.banner_pattern_fire_head": "Padrão de Estandarte", "item.iceandfire.banner_pattern_fire_head.desc": "Cabeça de Dragão de Fogo", "item.iceandfire.banner_pattern_fire_head.desc.black": "Cabeça de Dragão de Fogo Preta", "item.iceandfire.banner_pattern_fire_head.desc.blue": "Cabeça de Dragão de Fogo Azul", "item.iceandfire.banner_pattern_fire_head.desc.brown": "Cabeça de Dragão de Fogo Marrom", "item.iceandfire.banner_pattern_fire_head.desc.cyan": "Cabeça de Dragão de Fogo Ciano", "item.iceandfire.banner_pattern_fire_head.desc.gray": "Cabeça de Dragão de Fogo Cinza", "item.iceandfire.banner_pattern_fire_head.desc.green": "Cabeça de Dragão de Fogo Verde", "item.iceandfire.banner_pattern_fire_head.desc.light_blue": "Cabeça de Dragão de Fogo Azul Claro", "item.iceandfire.banner_pattern_fire_head.desc.light_gray": "Cabeça de Dragão de Fogo Cinza Claro", "item.iceandfire.banner_pattern_fire_head.desc.lime": "Cabeça de Dragão de Fogo Lima", "item.iceandfire.banner_pattern_fire_head.desc.magenta": "Cabeça de Dragão de Fogo Magenta", "item.iceandfire.banner_pattern_fire_head.desc.orange": "Cabeça de Dragão de Fogo Laranja", "item.iceandfire.banner_pattern_fire_head.desc.pink": "Cabeça de Dragão de Fogo Rosa", "item.iceandfire.banner_pattern_fire_head.desc.purple": "Cabeça de Dragão de Fogo Roxa", "item.iceandfire.banner_pattern_fire_head.desc.red": "Cabeça de Dragão de Fogo Vermelha", "item.iceandfire.banner_pattern_fire_head.desc.white": "Cabeça de Dragão de Fogo Branca", "item.iceandfire.banner_pattern_fire_head.desc.yellow": "Cabeça de Dragão de Fogo Amarela", "item.iceandfire.banner_pattern_gorgon": "Padrão de Estandarte", "item.iceandfire.banner_pattern_gorgon.desc": "Cabeça de Górgona", "item.iceandfire.banner_pattern_gorgon.desc.black": "Cabeça de Górgona Preta", "item.iceandfire.banner_pattern_gorgon.desc.blue": "Cabeça de Górgona Azul", "item.iceandfire.banner_pattern_gorgon.desc.brown": "Cabeça de Górgona Marrom", "item.iceandfire.banner_pattern_gorgon.desc.cyan": "Cabeça de Górgona Ciano", "item.iceandfire.banner_pattern_gorgon.desc.gray": "Cabeça de Górgona Cinza", "item.iceandfire.banner_pattern_gorgon.desc.green": "Cabeça de Górgona Verde", "item.iceandfire.banner_pattern_gorgon.desc.light_blue": "Cabeça de Górgona Azul Claro", "item.iceandfire.banner_pattern_gorgon.desc.light_gray": "Cabeça de Górgona Cinza Claro", "item.iceandfire.banner_pattern_gorgon.desc.lime": "Cabeça de Górgona Lima", "item.iceandfire.banner_pattern_gorgon.desc.magenta": "Cabeça de Górgona Magenta", "item.iceandfire.banner_pattern_gorgon.desc.orange": "Cabeça de Górgona Laranja", "item.iceandfire.banner_pattern_gorgon.desc.pink": "Cabeça de Górgona Rosa", "item.iceandfire.banner_pattern_gorgon.desc.purple": "Cabeça de Górgona Roxa", "item.iceandfire.banner_pattern_gorgon.desc.red": "Cabeça de Górgona Vermelha", "item.iceandfire.banner_pattern_gorgon.desc.white": "Cabeça de Górgona Branca", "item.iceandfire.banner_pattern_gorgon.desc.yellow": "Cabeça de Górgona Amarela", "item.iceandfire.banner_pattern_hippocampus": "Padrão de Estandarte", "item.iceandfire.banner_pattern_hippocampus.desc": "Hipocampo", "item.iceandfire.banner_pattern_hippocampus.desc.black": "Hipocampo Preto", "item.iceandfire.banner_pattern_hippocampus.desc.blue": "Hipocampo Azul", "item.iceandfire.banner_pattern_hippocampus.desc.brown": "Hipocampo Marrom", "item.iceandfire.banner_pattern_hippocampus.desc.cyan": "Hipocampo Ciano", "item.iceandfire.banner_pattern_hippocampus.desc.gray": "Hipocampo Cinza", "item.iceandfire.banner_pattern_hippocampus.desc.green": "Hipocampo Verde", "item.iceandfire.banner_pattern_hippocampus.desc.light_blue": "Hipocampo Azul Claro", "item.iceandfire.banner_pattern_hippocampus.desc.light_gray": "Hipocampo Cinza Claro", "item.iceandfire.banner_pattern_hippocampus.desc.lime": "Hipocampo Lima", "item.iceandfire.banner_pattern_hippocampus.desc.magenta": "Hipocampo Magenta", "item.iceandfire.banner_pattern_hippocampus.desc.orange": "Hipocampo <PERSON>", "item.iceandfire.banner_pattern_hippocampus.desc.pink": "Hipocampo Rosa", "item.iceandfire.banner_pattern_hippocampus.desc.purple": "Hipocampo Roxo", "item.iceandfire.banner_pattern_hippocampus.desc.red": "Hipocampo Vermelho", "item.iceandfire.banner_pattern_hippocampus.desc.white": "Hipocampo Branco", "item.iceandfire.banner_pattern_hippocampus.desc.yellow": "Hipocampo <PERSON>elo", "item.iceandfire.banner_pattern_hippogryph_head": "Padrão de Estandarte", "item.iceandfire.banner_pattern_hippogryph_head.desc": "Cabeça de Hipogrifo", "item.iceandfire.banner_pattern_hippogryph_head.desc.black": "Cabeça de Hipogrifo Preta", "item.iceandfire.banner_pattern_hippogryph_head.desc.blue": "Cabeça de Hipogrifo <PERSON>l", "item.iceandfire.banner_pattern_hippogryph_head.desc.brown": "Cabeça de Hipogrifo Marrom", "item.iceandfire.banner_pattern_hippogryph_head.desc.cyan": "Cabeça de Hipogrifo <PERSON>", "item.iceandfire.banner_pattern_hippogryph_head.desc.gray": "Cabeça de Hipogrifo <PERSON>inza", "item.iceandfire.banner_pattern_hippogryph_head.desc.green": "Cabeça de Hipogrifo Verde", "item.iceandfire.banner_pattern_hippogryph_head.desc.light_blue": "Cabeça de Hipogrifo Azul Claro", "item.iceandfire.banner_pattern_hippogryph_head.desc.light_gray": "Cabeça de Hipogrifo Cinza Claro", "item.iceandfire.banner_pattern_hippogryph_head.desc.lime": "Cabeça de Hipogrifo Lima", "item.iceandfire.banner_pattern_hippogryph_head.desc.magenta": "Cabeça de Hipogrifo <PERSON>", "item.iceandfire.banner_pattern_hippogryph_head.desc.orange": "Cabeça de Hipogrifo <PERSON>", "item.iceandfire.banner_pattern_hippogryph_head.desc.pink": "Cabeça de Hipogrifo Rosa", "item.iceandfire.banner_pattern_hippogryph_head.desc.purple": "Cabeça de Hipogrifo <PERSON>", "item.iceandfire.banner_pattern_hippogryph_head.desc.red": "Cabeça de Hipogrifo <PERSON>", "item.iceandfire.banner_pattern_hippogryph_head.desc.white": "Cabeça de Hipogrifo Branca", "item.iceandfire.banner_pattern_hippogryph_head.desc.yellow": "Cabeça de Hipogrifo <PERSON>ela", "item.iceandfire.banner_pattern_ice": "Padrão de Estandarte", "item.iceandfire.banner_pattern_ice.desc": "Dragão de Gelo", "item.iceandfire.banner_pattern_ice.desc.black": "Dragão de Gelo Preto", "item.iceandfire.banner_pattern_ice.desc.blue": "Dragão de Gelo Azul", "item.iceandfire.banner_pattern_ice.desc.brown": "Dragão de Gelo Marrom", "item.iceandfire.banner_pattern_ice.desc.cyan": "Dragão de Gelo Ciano", "item.iceandfire.banner_pattern_ice.desc.gray": "Dragão de Gelo Cinza", "item.iceandfire.banner_pattern_ice.desc.green": "Dragão de Gelo Verde", "item.iceandfire.banner_pattern_ice.desc.light_blue": "Dragão de Gelo Azul Claro", "item.iceandfire.banner_pattern_ice.desc.light_gray": "Dragão de Gelo Cinza Claro", "item.iceandfire.banner_pattern_ice.desc.lime": "Dragão de Gelo Lima", "item.iceandfire.banner_pattern_ice.desc.magenta": "Dragão de Gelo Magenta", "item.iceandfire.banner_pattern_ice.desc.orange": "Dragão de Gelo Laranja", "item.iceandfire.banner_pattern_ice.desc.pink": "Dragão de Gelo Rosa", "item.iceandfire.banner_pattern_ice.desc.purple": "Dragão de Gelo Roxo", "item.iceandfire.banner_pattern_ice.desc.red": "Dragão de Gelo Vermelho", "item.iceandfire.banner_pattern_ice.desc.white": "Dragão de Gelo Branco", "item.iceandfire.banner_pattern_ice.desc.yellow": "Dragão de Gelo Amarelo", "item.iceandfire.banner_pattern_ice_head": "Padrão de Estandarte", "item.iceandfire.banner_pattern_ice_head.desc": "Cabeça de Dragão de Gelo", "item.iceandfire.banner_pattern_ice_head.desc.black": "Cabeça de Dragão de Gelo Preta", "item.iceandfire.banner_pattern_ice_head.desc.blue": "Cabeça de Dragão de Gelo Azul", "item.iceandfire.banner_pattern_ice_head.desc.brown": "Cabeça de Dragão de Gelo Marrom", "item.iceandfire.banner_pattern_ice_head.desc.cyan": "Cabeça de Dragão de Gelo Ciano", "item.iceandfire.banner_pattern_ice_head.desc.gray": "Cabeça de Dragão de Gelo Cinza", "item.iceandfire.banner_pattern_ice_head.desc.green": "Cabeça de Dragão de Gelo Verde", "item.iceandfire.banner_pattern_ice_head.desc.light_blue": "Cabeça de Dragão de Gelo Azul Claro", "item.iceandfire.banner_pattern_ice_head.desc.light_gray": "Cabeça de Dragão de Gelo Cinza Claro", "item.iceandfire.banner_pattern_ice_head.desc.lime": "Cabeça de Dragão de Gelo Lima", "item.iceandfire.banner_pattern_ice_head.desc.magenta": "Cabeça de Dragão de Gelo Magenta", "item.iceandfire.banner_pattern_ice_head.desc.orange": "Cabeça de Dragão de Gelo Laranja", "item.iceandfire.banner_pattern_ice_head.desc.pink": "Cabeça de Dragão de Gelo Rosa", "item.iceandfire.banner_pattern_ice_head.desc.purple": "Cabeça de Dragão de Gelo Roxa", "item.iceandfire.banner_pattern_ice_head.desc.red": "Cabeça de Dragão de Gelo Vermelha", "item.iceandfire.banner_pattern_ice_head.desc.white": "Cabeça de Dragão de Gelo Branca", "item.iceandfire.banner_pattern_ice_head.desc.yellow": "Cabeça de Dragão de Gelo Amarela", "item.iceandfire.banner_pattern_lightning": "Padrão de Estandarte", "item.iceandfire.banner_pattern_lightning.desc": "Dragão do Raio", "item.iceandfire.banner_pattern_lightning.desc.black": "Dragão do Raio Preto", "item.iceandfire.banner_pattern_lightning.desc.blue": "Dragão do Raio Azul", "item.iceandfire.banner_pattern_lightning.desc.brown": "Dragão do Raio Marrom", "item.iceandfire.banner_pattern_lightning.desc.cyan": "Dragão do Raio Ciano", "item.iceandfire.banner_pattern_lightning.desc.gray": "Dragão do Raio Cinza", "item.iceandfire.banner_pattern_lightning.desc.green": "Dragão do Raio Verde", "item.iceandfire.banner_pattern_lightning.desc.light_blue": "Dragão do Raio Azul Claro", "item.iceandfire.banner_pattern_lightning.desc.light_gray": "Dragão do Raio Cinza Claro", "item.iceandfire.banner_pattern_lightning.desc.lime": "Dragão do Raio Lima", "item.iceandfire.banner_pattern_lightning.desc.magenta": "Dragão do Raio Magenta", "item.iceandfire.banner_pattern_lightning.desc.orange": "Dragão do Raio Laranja", "item.iceandfire.banner_pattern_lightning.desc.pink": "Dragão do Raio Rosa", "item.iceandfire.banner_pattern_lightning.desc.purple": "Dragão do Raio Roxo", "item.iceandfire.banner_pattern_lightning.desc.red": "Dragão do Raio Vermelho", "item.iceandfire.banner_pattern_lightning.desc.white": "Dragão do Raio Branco", "item.iceandfire.banner_pattern_lightning.desc.yellow": "Dragão do Raio Amarelo", "item.iceandfire.banner_pattern_lightning_head": "Padrão de Estandarte", "item.iceandfire.banner_pattern_lightning_head.desc": "Cabeça de Dragão do Raio", "item.iceandfire.banner_pattern_lightning_head.desc.black": "Cabeça de Dragão do Raio Preta", "item.iceandfire.banner_pattern_lightning_head.desc.blue": "Cabeça de Dragão do Raio Azul", "item.iceandfire.banner_pattern_lightning_head.desc.brown": "Cabeça de Dragão do Raio Marrom", "item.iceandfire.banner_pattern_lightning_head.desc.cyan": "Cabeça de Dragão do Raio Ciano", "item.iceandfire.banner_pattern_lightning_head.desc.gray": "Cabeça de Dragão do Raio Cinza", "item.iceandfire.banner_pattern_lightning_head.desc.green": "Cabeça de Dragão do Raio Verde", "item.iceandfire.banner_pattern_lightning_head.desc.light_blue": "Cabeça de Dragão do Raio Azul Claro", "item.iceandfire.banner_pattern_lightning_head.desc.light_gray": "Cabeça de Dragão do Raio Cinza Claro", "item.iceandfire.banner_pattern_lightning_head.desc.lime": "Cabeça de Dragão do Raio Lima", "item.iceandfire.banner_pattern_lightning_head.desc.magenta": "Cabeça de Dragão do Raio Magenta", "item.iceandfire.banner_pattern_lightning_head.desc.orange": "Cabeça de Dragão do Raio Laranja", "item.iceandfire.banner_pattern_lightning_head.desc.pink": "Cabeça de Dragão do Raio Rosa", "item.iceandfire.banner_pattern_lightning_head.desc.purple": "Cabeça de Dragão do Raio Roxa", "item.iceandfire.banner_pattern_lightning_head.desc.red": "Cabeça de Dragão do Raio Vermelha", "item.iceandfire.banner_pattern_lightning_head.desc.white": "Cabeça de Dragão do Raio Branca", "item.iceandfire.banner_pattern_lightning_head.desc.yellow": "Cabeça de Dragão do Raio Amarela", "item.iceandfire.banner_pattern_mermaid": "Padrão de Estandarte", "item.iceandfire.banner_pattern_mermaid.desc": "Serei<PERSON>", "item.iceandfire.banner_pattern_mermaid.desc.black": "Sereia <PERSON>ta", "item.iceandfire.banner_pattern_mermaid.desc.blue": "<PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_mermaid.desc.brown": "<PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_mermaid.desc.cyan": "<PERSON>eia <PERSON>", "item.iceandfire.banner_pattern_mermaid.desc.gray": "<PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_mermaid.desc.green": "Sereia Verde", "item.iceandfire.banner_pattern_mermaid.desc.light_blue": "Sereia A<PERSON>l <PERSON>o", "item.iceandfire.banner_pattern_mermaid.desc.light_gray": "Sereia Cin<PERSON> Claro", "item.iceandfire.banner_pattern_mermaid.desc.lime": "Sereia <PERSON>", "item.iceandfire.banner_pattern_mermaid.desc.magenta": "<PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_mermaid.desc.orange": "<PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_mermaid.desc.pink": "<PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_mermaid.desc.purple": "<PERSON><PERSON><PERSON>a", "item.iceandfire.banner_pattern_mermaid.desc.red": "<PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_mermaid.desc.white": "Sereia Branca", "item.iceandfire.banner_pattern_mermaid.desc.yellow": "<PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_sea_serpent": "Padrão de Estandarte", "item.iceandfire.banner_pattern_sea_serpent.desc": "<PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_sea_serpent.desc.black": "<PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_sea_serpent.desc.blue": "<PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_sea_serpent.desc.brown": "<PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_sea_serpent.desc.cyan": "<PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_sea_serpent.desc.gray": "<PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_sea_serpent.desc.green": "<PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_sea_serpent.desc.light_blue": "<PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_sea_serpent.desc.light_gray": "<PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_sea_serpent.desc.lime": "<PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_sea_serpent.desc.magenta": "<PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_sea_serpent.desc.orange": "<PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_sea_serpent.desc.pink": "<PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_sea_serpent.desc.purple": "<PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_sea_serpent.desc.red": "<PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_sea_serpent.desc.white": "Serpente <PERSON>", "item.iceandfire.banner_pattern_sea_serpent.desc.yellow": "<PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_troll": "Padrão de Estandarte", "item.iceandfire.banner_pattern_troll.desc": "Cabeça de Troll", "item.iceandfire.banner_pattern_troll.desc.black": "Cabeça de Troll Preta", "item.iceandfire.banner_pattern_troll.desc.blue": "Cabeça de Troll Azul", "item.iceandfire.banner_pattern_troll.desc.brown": "Cabeça de Troll Marrom", "item.iceandfire.banner_pattern_troll.desc.cyan": "Cabeça de Troll Ciano", "item.iceandfire.banner_pattern_troll.desc.gray": "Cabeça de Troll Cinza", "item.iceandfire.banner_pattern_troll.desc.green": "Cabeça de Troll Verde", "item.iceandfire.banner_pattern_troll.desc.light_blue": "Cabeça de Troll Azul Claro", "item.iceandfire.banner_pattern_troll.desc.light_gray": "Cabeça de Troll Cinza Claro", "item.iceandfire.banner_pattern_troll.desc.lime": "Cabeça de Troll Lima", "item.iceandfire.banner_pattern_troll.desc.magenta": "Cabeça de Troll Magenta", "item.iceandfire.banner_pattern_troll.desc.orange": "Cabeça de Troll Laranja", "item.iceandfire.banner_pattern_troll.desc.pink": "Cabeça de Troll Rosa", "item.iceandfire.banner_pattern_troll.desc.purple": "Cabeça de Troll Roxa", "item.iceandfire.banner_pattern_troll.desc.red": "Cabeça de Troll Vermelha", "item.iceandfire.banner_pattern_troll.desc.white": "Cabeça de Troll Branca", "item.iceandfire.banner_pattern_troll.desc.yellow": "Cabeça de Troll Amarela", "item.iceandfire.banner_pattern_weezer": "Padrão de Estandarte", "item.iceandfire.banner_pattern_weezer.desc": "Weezer", "item.iceandfire.banner_pattern_weezer.desc.black": "<PERSON><PERSON>", "item.iceandfire.banner_pattern_weezer.desc.blue": "We<PERSON>", "item.iceandfire.banner_pattern_weezer.desc.brown": "We<PERSON>", "item.iceandfire.banner_pattern_weezer.desc.cyan": "We<PERSON>", "item.iceandfire.banner_pattern_weezer.desc.gray": "We<PERSON>", "item.iceandfire.banner_pattern_weezer.desc.green": "Weezer Verde", "item.iceandfire.banner_pattern_weezer.desc.light_blue": "Weezer <PERSON>", "item.iceandfire.banner_pattern_weezer.desc.light_gray": "Weezer <PERSON>", "item.iceandfire.banner_pattern_weezer.desc.lime": "We<PERSON>", "item.iceandfire.banner_pattern_weezer.desc.magenta": "We<PERSON>", "item.iceandfire.banner_pattern_weezer.desc.orange": "We<PERSON>", "item.iceandfire.banner_pattern_weezer.desc.pink": "We<PERSON>", "item.iceandfire.banner_pattern_weezer.desc.purple": "We<PERSON>", "item.iceandfire.banner_pattern_weezer.desc.red": "<PERSON><PERSON>", "item.iceandfire.banner_pattern_weezer.desc.white": "We<PERSON>", "item.iceandfire.banner_pattern_weezer.desc.yellow": "We<PERSON>", "item.iceandfire.bestiary": "<PERSON><PERSON><PERSON><PERSON>", "item.iceandfire.blindfold": "<PERSON><PERSON><PERSON>", "item.iceandfire.cannoli": "§6Cannoli§r", "item.iceandfire.cannoli.desc": "\"Você pode encontrar dragões ou rufiões\"", "item.iceandfire.chain": "<PERSON><PERSON>", "item.iceandfire.chain.desc_0": "Semelhante a uma guia, pode ser amarrada a blocos de parede", "item.iceandfire.chain.desc_1": "<PERSON>ão pode ser quebrada, pode ser usada na maioria dos mobs", "item.iceandfire.chain_sticky": "<PERSON><PERSON>", "item.iceandfire.chain_sticky.desc_2": "Usada para conectar dois mobs acorrentados", "item.iceandfire.chain_sticky.desc_3": "Perde a pegajosidade após o uso", "item.iceandfire.cockatrice_eye": "<PERSON><PERSON><PERSON>", "item.iceandfire.cockatrice_eye.desc_0": "§6Drop raro§r", "item.iceandfire.cockatrice_scepter": "<PERSON><PERSON> de Cocatrice", "item.iceandfire.cockatrice_scepter.desc_0": "Aplica Wither no alvo quando usado", "item.iceandfire.cockatrice_scepter.desc_1": "Pode atingir múltiplas entidades próximas", "item.iceandfire.cockatrice_skull": "Crânio de Cocatrice", "item.iceandfire.cockatrice_skull.jei_desc": "Pode ser colocado no chão ou em paredes como um troféu.", "item.iceandfire.cooked_rice_with_fire_dragon_meat": "Arroz Frito com Carne de Dragão de Fogo", "item.iceandfire.cooked_rice_with_ice_dragon_meat": "Arroz Frito com Carne de Dragão de Gelo", "item.iceandfire.cooked_rice_with_lightning_dragon_meat": "Arroz Frito com Carne de Dragão do Raio", "item.iceandfire.copper_axe": "<PERSON><PERSON><PERSON>", "item.iceandfire.copper_hoe": "Enxada de Cobre", "item.iceandfire.copper_nugget": "Pepita de Cobre", "item.iceandfire.copper_pickaxe": "Picareta de Cobre", "item.iceandfire.copper_shovel": "Pá de Cobre", "item.iceandfire.copper_sword": "Espada de Cobre", "item.iceandfire.creative_dragon_meal": "Comida de Dragão Criativa", "item.iceandfire.creative_dragon_meal.desc_0": "Doma instantaneamente qualquer <PERSON>", "item.iceandfire.creative_dragon_meal.desc_1": "Apenas Modo Criativo", "item.iceandfire.custom_banner.jei_desc": "Tem um padrão de estandarte único!", "item.iceandfire.cyclops_eye": "<PERSON><PERSON><PERSON> Ciclope", "item.iceandfire.cyclops_eye.desc_0": "Arma de Área de Efeito", "item.iceandfire.cyclops_eye.desc_1": "Inflige fraqueza a todos os hostis próximos quando segurado", "item.iceandfire.cyclops_skull": "Crânio de Ciclope", "item.iceandfire.cyclops_skull.jei_desc": "Pode ser colocado no chão ou em paredes como um troféu.", "item.iceandfire.deathworm_chitin_red": "<PERSON><PERSON><PERSON> Ver<PERSON> da Morte Vermelha", "item.iceandfire.deathworm_chitin_white": "<PERSON><PERSON><PERSON> de Verme da Morte Branca", "item.iceandfire.deathworm_chitin_yellow": "<PERSON><PERSON><PERSON> Verme da Morte Bronzeada", "item.iceandfire.deathworm_egg": "<PERSON><PERSON> <PERSON> Verme <PERSON> Mo<PERSON>", "item.iceandfire.deathworm_egg_giant": "<PERSON><PERSON> de Verme da Morte Gigante", "item.iceandfire.deathworm_gauntlet.desc_0": "Açoita o alvo com sua língua causando 3 de dano", "item.iceandfire.deathworm_gauntlet.desc_1": "Puxa o alvo para o jogador", "item.iceandfire.deathworm_gauntlet_red": "<PERSON><PERSON><PERSON> <PERSON> Ver<PERSON>", "item.iceandfire.deathworm_gauntlet_white": "<PERSON><PERSON><PERSON> <PERSON> Ver<PERSON>", "item.iceandfire.deathworm_gauntlet_yellow": "<PERSON><PERSON><PERSON> <PERSON> Ver<PERSON>", "item.iceandfire.deathworm_red_boots": "Botas de Quitina de Verme da Morte Vermelha", "item.iceandfire.deathworm_red_chestplate": "<PERSON><PERSON><PERSON> <PERSON> Quitina de Verme da Morte Vermelha", "item.iceandfire.deathworm_red_helmet": "<PERSON><PERSON> Verme da Morte Vermelha", "item.iceandfire.deathworm_red_leggings": "Calças de Quitina de Verme da Morte Vermelha", "item.iceandfire.deathworm_tounge": "Língua de Verme da Morte", "item.iceandfire.deathworm_tounge.desc_0": "§6Drop raro§r", "item.iceandfire.deathworm_white_boots": "Botas de Quitina de Verme da Morte Branca", "item.iceandfire.deathworm_white_chestplate": "Peitoral <PERSON> Quitina de Verme da Morte Branca", "item.iceandfire.deathworm_white_helmet": "<PERSON><PERSON> Quitina de Verme da Morte Branca", "item.iceandfire.deathworm_white_leggings": "Calças de Quitina de Verme da Morte Branca", "item.iceandfire.deathworm_yellow_boots": "Botas de Quitina de Verme da Morte Bronzeada", "item.iceandfire.deathworm_yellow_chestplate": "<PERSON><PERSON><PERSON> <PERSON> Quitina de Verme da Morte Bronzeada", "item.iceandfire.deathworm_yellow_helmet": "<PERSON><PERSON> Verme da Morte Bronzeada", "item.iceandfire.deathworm_yellow_leggings": "Calças de Quitina de Verme da Morte Bronzeada", "item.iceandfire.diamond_hippogryph_armor": "Armadura de Diamante para Hipogrifo", "item.iceandfire.dragon_boots": "Botas de Escama de Dragão", "item.iceandfire.dragon_chestplate": "Peitoral de Escama de Dragão", "item.iceandfire.dragon_debug_stick": "Bastão de Depuração de Dragão", "item.iceandfire.dragon_debug_stick.desc_0": "Apenas para uso de desenvolvedores...", "item.iceandfire.dragon_flute": "Flauta de Osso de Dragão", "item.iceandfire.dragon_helmet": "<PERSON><PERSON> de Escama de Dragão", "item.iceandfire.dragon_horn": "<PERSON><PERSON><PERSON>", "item.iceandfire.dragon_horn_fire": "<PERSON><PERSON><PERSON>", "item.iceandfire.dragon_horn_ice": "<PERSON><PERSON><PERSON>", "item.iceandfire.dragon_leggings": "Calças de Escama de Dragão", "item.iceandfire.dragon_meal": "Comida de Dragão", "item.iceandfire.dragon_seeker": "Buscador de Dragões", "item.iceandfire.dragon_seeker.credit": "Portado do mod Ice and Fire: Dragonseeker por Syrikal", "item.iceandfire.dragon_seeker.found": "Encontrar <PERSON>", "item.iceandfire.dragon_seeker.found_location": "O dragão mais próximo está em ", "item.iceandfire.dragon_seeker.not_found": "Não foi possível encontrar o dragão", "item.iceandfire.dragon_seeker.tooltip": "Rastreia o dragão mais próximo em 150 blocos", "item.iceandfire.dragon_skull_fire": "Crânio de Dragão", "item.iceandfire.dragon_skull_fire.jei_desc": "Pode ser colocado no chão ou em paredes como um troféu.", "item.iceandfire.dragon_skull_ice": "Crânio de Dragão", "item.iceandfire.dragon_skull_ice.jei_desc": "Pode ser colocado no chão ou em paredes como um troféu.", "item.iceandfire.dragon_skull_lightning": "Crânio de Dragão", "item.iceandfire.dragon_skull_lightning.jei_desc": "Pode ser colocado no chão ou em paredes como um troféu.", "item.iceandfire.dragon_stick": "Cajado de Comando de Dragão", "item.iceandfire.dragonarmor_copper": "Armadura de Dragão de Cobre", "item.iceandfire.dragonarmor_diamond": "Armadura de Dragão de Diamante", "item.iceandfire.dragonarmor_dragon_steel_fire": "Armadura de Dragão de Aço de Dragão de Fogo", "item.iceandfire.dragonarmor_dragon_steel_ice": "Armadura de Dragão de Aço de Dragão de Gelo", "item.iceandfire.dragonarmor_dragon_steel_lightning": "Armadura de Dragão de Aço de Dragão do Raio", "item.iceandfire.dragonarmor_gold": "Armadura de Dragão de Ouro", "item.iceandfire.dragonarmor_iron": "Armadura de Dragão de Ferro", "item.iceandfire.dragonarmor_netherite": "Armadura de Dragão de Netherita", "item.iceandfire.dragonarmor_silver": "Armadura de Dragão de Prata", "item.iceandfire.dragonbone": "<PERSON><PERSON> de Dragão", "item.iceandfire.dragonbone_arrow": "Flecha de Osso de Dragão", "item.iceandfire.dragonbone_axe": "<PERSON><PERSON><PERSON> de Dragão", "item.iceandfire.dragonbone_bow": "Arco de Osso de Dragão", "item.iceandfire.dragonbone_hoe": "Enxada de Osso de Dragão", "item.iceandfire.dragonbone_pickaxe": "Picareta de Osso de Dragão", "item.iceandfire.dragonbone_shovel": "Pá de Osso de Dragão", "item.iceandfire.dragonbone_sword": "Espada de Osso de Dragão", "item.iceandfire.dragonbone_sword_fire": "Espada de Osso de Dragão Flamejante", "item.iceandfire.dragonbone_sword_ice": "Espada de Osso de Dragão Gelada", "item.iceandfire.dragonbone_sword_lightning": "Espada de Osso de Dragão do Raio", "item.iceandfire.dragonegg": "Ovo <PERSON>", "item.iceandfire.dragonegg.jei_desc": "Coloque em uma chama aberta se for um dragão de fogo, debaixo d'água se for dragão de gelo, ou na chuva se for dragão do raio e espere um bom tempo. O dragão se vinculará ao jogador mais próximo.", "item.iceandfire.dragonscales": "Escamas de Dragão", "item.iceandfire.dragonsteel_fire_axe": "Machado de Aço de Dragão de Fogo", "item.iceandfire.dragonsteel_fire_boots": "Botas de Aço de Dragão de Fogo", "item.iceandfire.dragonsteel_fire_chestplate": "Peitoral de Aço de Dragão de Fogo", "item.iceandfire.dragonsteel_fire_helmet": "Elmo de Aço de Dragão de Fogo", "item.iceandfire.dragonsteel_fire_hoe": "Enxada de Aço de Dragão de Fogo", "item.iceandfire.dragonsteel_fire_ingot": "Lingote de Aço de Dragão de Fogo", "item.iceandfire.dragonsteel_fire_leggings": "Calças de Aço de Dragão de Fogo", "item.iceandfire.dragonsteel_fire_pickaxe": "Picareta de Aço de Dragão de Fogo", "item.iceandfire.dragonsteel_fire_shovel": "Pá de Aço de Dragão de Fogo", "item.iceandfire.dragonsteel_fire_sword": "Espada de Aço de Dragão de Fogo", "item.iceandfire.dragonsteel_ice_axe": "Machado de Aço de Dragão de Gelo", "item.iceandfire.dragonsteel_ice_boots": "Botas de Aço de Dragão de Gelo", "item.iceandfire.dragonsteel_ice_chestplate": "Peitoral de Aço de Dragão de Gelo", "item.iceandfire.dragonsteel_ice_helmet": "Elmo de Aço de Dragão de Gelo", "item.iceandfire.dragonsteel_ice_hoe": "Enxada de Aço de Dragão de Gelo", "item.iceandfire.dragonsteel_ice_ingot": "Lingote de Aço de Dragão de Gelo", "item.iceandfire.dragonsteel_ice_leggings": "Calças de Aço de Dragão de Gelo", "item.iceandfire.dragonsteel_ice_pickaxe": "Picareta de Aço de Dragão de Gelo", "item.iceandfire.dragonsteel_ice_shovel": "Pá de Aço de Dragão de Gelo", "item.iceandfire.dragonsteel_ice_sword": "Espada de Aço de Dragão de Gelo", "item.iceandfire.dragonsteel_lightning_axe": "Machado de Aço de Dragão do Raio", "item.iceandfire.dragonsteel_lightning_boots": "Botas de Aço de Dragão do Raio", "item.iceandfire.dragonsteel_lightning_chestplate": "Peitoral de Aço de Dragão do Raio", "item.iceandfire.dragonsteel_lightning_helmet": "Elmo de Aço de Dragão do Raio", "item.iceandfire.dragonsteel_lightning_hoe": "Enxada de Aço de Dragão do Raio", "item.iceandfire.dragonsteel_lightning_ingot": "Lingote de Aço de Dragão do Raio", "item.iceandfire.dragonsteel_lightning_leggings": "Calças de Aço de Dragão do Raio", "item.iceandfire.dragonsteel_lightning_pickaxe": "Picareta de Aço de Dragão do Raio", "item.iceandfire.dragonsteel_lightning_shovel": "Pá de Aço de Dragão do Raio", "item.iceandfire.dragonsteel_lightning_sword": "Espada de Aço de Dragão do Raio", "item.iceandfire.dread_key": "§bChave do Mestre da Cripta§r", "item.iceandfire.dread_knight_sword": "Espada do Cavaleiro Sombrio", "item.iceandfire.dread_queen_staff": "<PERSON><PERSON><PERSON> <PERSON>ha <PERSON>", "item.iceandfire.dread_queen_sword": "<PERSON><PERSON><PERSON> da Rainha Sombria", "item.iceandfire.dread_shard": "Fragmento Sombrio", "item.iceandfire.dread_sword": "Espada do Servo Sombrio", "item.iceandfire.earplugs": "Tampões de Ouvido", "item.iceandfire.ectoplasm": "Ectoplasma", "item.iceandfire.epic_dragon_seeker": "Buscador de Dragões Épico", "item.iceandfire.epic_dragon_seeker.tooltip": "Rastreia o dragão vivo mais próximo em 200 blocos", "item.iceandfire.fire_dragon_blood": "Sangue de Dragão de Fogo", "item.iceandfire.fire_dragon_blood.jei_desc": "Obtido interagindo com o cadáver de um dragão segurando uma garrafa vazia.", "item.iceandfire.fire_dragon_flesh": "Carne de Dragão de Fogo", "item.iceandfire.fire_dragon_heart": "Coração de Dragão de Fogo", "item.iceandfire.fire_stew": "Mistura de Lírio de <PERSON>ogo", "item.iceandfire.fire_stew.jei_desc": "Usado para procriar dois dragões macho e fêmea. A fêmea criará um ninho com um ovo depois.", "item.iceandfire.fishing_spear": "Lança de Pesca", "item.iceandfire.forest_troll_leather_boots": "Botas de Couro de Troll da Floresta", "item.iceandfire.forest_troll_leather_chestplate": "Peitoral de Couro de Troll da Floresta", "item.iceandfire.forest_troll_leather_helmet": "<PERSON><PERSON> Couro de Troll da Floresta", "item.iceandfire.forest_troll_leather_leggings": "Calças de Couro de Troll da Floresta", "item.iceandfire.frost_stew": "Mistura de Lírio de Gelo", "item.iceandfire.frost_stew.jei_desc": "Usado para procriar dois dragões macho e fêmea. A fêmea criará um ninho com um ovo depois.", "item.iceandfire.frost_troll_leather_boots": "Botas de Couro de Troll do Gelo", "item.iceandfire.frost_troll_leather_chestplate": "Peitoral de Couro de Troll do Gelo", "item.iceandfire.frost_troll_leather_helmet": "Elmo de Couro de Troll do Gelo", "item.iceandfire.frost_troll_leather_leggings": "Calças de Couro de Troll do Gelo", "item.iceandfire.ghost_cream": "Creme Fantasma", "item.iceandfire.ghost_ingot": "Lingote Fantasmagórico", "item.iceandfire.ghost_ingot.desc_0": "§6Drop raro§r", "item.iceandfire.ghost_sword": "Lâmina Fantasmagórica", "item.iceandfire.ghost_sword.desc_0": "Dispara uma espada giratória que atravessa blocos", "item.iceandfire.ghost_sword.desc_1": "Projétil de espada causa dano extra", "item.iceandfire.godly_dragon_seeker": "Buscador de Dragões Divino", "item.iceandfire.godly_dragon_seeker.tooltip": "Obtém a localização do dragão selvagem vivo mais próximo em 500 blocos (Apenas Modo Criativo)", "item.iceandfire.gold_hippogryph_armor": "Armadura de Ouro para Hipogrifo", "item.iceandfire.gorgon_head": "Cabeça de Górgona", "item.iceandfire.hippocampus_fin": "Barbatana de Hipocampo", "item.iceandfire.hippocampus_fin.desc_0": "§6Drop raro§r", "item.iceandfire.hippocampus_slapper": "Tapa de Hipocampo", "item.iceandfire.hippocampus_slapper.desc_0": "§dArma Cômica§r", "item.iceandfire.hippocampus_slapper.desc_1": "Atordoa e retarda alvos; é bastante confuso ser atingido por uma cauda de peixe", "item.iceandfire.hippogryph_egg": "<PERSON><PERSON> de Hipogrifo", "item.iceandfire.hippogryph_skull": "Crânio de Hipogrifo", "item.iceandfire.hippogryph_skull.jei_desc": "Pode ser colocado no chão ou em paredes como um troféu.", "item.iceandfire.hippogryph_sword": "Espada de Garra de Hipogrifo", "item.iceandfire.hippogryph_sword.desc_0": "Sempre corta o alvo, causando dano extra", "item.iceandfire.hippogryph_sword.desc_1": "Cada uso é um ataque de varredura", "item.iceandfire.hippogryph_talon": "<PERSON><PERSON> de Hipogrifo", "item.iceandfire.hippogryph_talon.desc_0": "§6Drop raro§r", "item.iceandfire.hydra_arrow": "<PERSON><PERSON><PERSON>", "item.iceandfire.hydra_arrow.desc": "Envenena e drena vida do alvo", "item.iceandfire.hydra_fang": "Presa de Hidra", "item.iceandfire.hydra_heart": "Coração de Hidra", "item.iceandfire.hydra_heart.desc_0": "Quando na hotbar, concede regeneração crescente", "item.iceandfire.hydra_heart.desc_1": "com base em quão ferido o portador está", "item.iceandfire.hydra_skull": "Crân<PERSON>", "item.iceandfire.hydra_skull.jei_desc": "Pode ser colocado no chão ou em paredes como um troféu.", "item.iceandfire.ice_dragon_blood": "Sangue de Dragão de Gelo", "item.iceandfire.ice_dragon_blood.jei_desc": "Obtido interagindo com o cadáver de um dragão segurando uma garrafa vazia.", "item.iceandfire.ice_dragon_flesh": "Carne de Dragão de Gelo", "item.iceandfire.ice_dragon_heart": "Coração de Dragão de Gelo", "item.iceandfire.iron_hippogryph_armor": "Armadura de Ferro para Hipogrifo", "item.iceandfire.legendary_dragon_seeker": "Buscador de Dragões Lendário", "item.iceandfire.legendary_dragon_seeker.tooltip": "Rastreia o dragão selvagem vivo mais próximo em 300 blocos", "item.iceandfire.legendary_weapon.desc": "§6<PERSON><PERSON>", "item.iceandfire.lich_staff": "Cajado do Lich Sombrio", "item.iceandfire.lightning_dragon_blood": "Sangue de Dragão do Raio", "item.iceandfire.lightning_dragon_blood.jei_desc": "Obtido interagindo com o cadáver de um dragão segurando uma garrafa vazia.", "item.iceandfire.lightning_dragon_flesh": "Carne de Dragão do Raio", "item.iceandfire.lightning_dragon_heart": "Coração de Dragão do Raio", "item.iceandfire.lightning_stew": "Mistura de Lírio do Raio", "item.iceandfire.lightning_stew.jei_desc": "Usado para procriar dois dragões macho e fêmea. A fêmea criará um ninho com um ovo depois.", "item.iceandfire.manuscript": "Manus<PERSON>rito", "item.iceandfire.mountain_troll_leather_boots": "Botas de Couro de Troll da Montanha", "item.iceandfire.mountain_troll_leather_chestplate": "<PERSON><PERSON><PERSON> <PERSON> T<PERSON> da Montanha", "item.iceandfire.mountain_troll_leather_helmet": "<PERSON><PERSON> T<PERSON> da Montanha", "item.iceandfire.mountain_troll_leather_leggings": "Calças de Couro de Troll da Montanha", "item.iceandfire.netherite_hippogryph_armor": "Armadura de Netherita para Hipogrifo", "item.iceandfire.pixie_dust": "Pó de Pixie", "item.iceandfire.pixie_dust_milky_tea": "Chá com Leite e Pó de Pixie", "item.iceandfire.pixie_wand": "<PERSON><PERSON><PERSON><PERSON>", "item.iceandfire.pixie_wand.desc_0": "Dispara uma carga mágica que causa 5 de dano e faz o alvo levitar", "item.iceandfire.pixie_wand.desc_1": "Usa Pó de Pixie como Munição", "item.iceandfire.pixie_wings": "<PERSON><PERSON> de Pixie", "item.iceandfire.pixie_wings.desc_0": "§6Drop raro§r", "item.iceandfire.raw_silver": "<PERSON><PERSON>", "item.iceandfire.rotten_egg": "<PERSON><PERSON>", "item.iceandfire.sapphire_gem": "Safira", "item.iceandfire.sea_serpent_armor.desc_0": "Fornece Respiração Aquática", "item.iceandfire.sea_serpent_armor.desc_1": "Fornece Força quando molhado, aumentando os níveis com o conjunto completo", "item.iceandfire.sea_serpent_arrow": "<PERSON><PERSON><PERSON>", "item.iceandfire.sea_serpent_arrow.desc": "Não é afetada pela água!", "item.iceandfire.sea_serpent_boots": "Botas do Guardião da Maré", "item.iceandfire.sea_serpent_chestplate": "Peitoral <PERSON> Guardi<PERSON> da Maré", "item.iceandfire.sea_serpent_fang": "Presa <PERSON>pent<PERSON>", "item.iceandfire.sea_serpent_helmet": "Elmo do Guardião da Maré", "item.iceandfire.sea_serpent_leggings": "Calças do Guardião da Maré", "item.iceandfire.sea_serpent_scales_blue": "<PERSON><PERSON><PERSON>", "item.iceandfire.sea_serpent_scales_bronze": "<PERSON><PERSON><PERSON>", "item.iceandfire.sea_serpent_scales_deepblue": "<PERSON><PERSON><PERSON>", "item.iceandfire.sea_serpent_scales_green": "<PERSON><PERSON><PERSON>", "item.iceandfire.sea_serpent_scales_purple": "<PERSON><PERSON><PERSON>", "item.iceandfire.sea_serpent_scales_red": "<PERSON><PERSON><PERSON>", "item.iceandfire.sea_serpent_scales_teal": "<PERSON><PERSON><PERSON>", "item.iceandfire.seaserpent_skull": "<PERSON><PERSON><PERSON><PERSON>e <PERSON>", "item.iceandfire.seaserpent_skull.jei_desc": "Pode ser colocado no chão ou em paredes como um troféu.", "item.iceandfire.sheep_boots": "Botas de Disfarce de Ovelha", "item.iceandfire.sheep_chestplate": "<PERSON><PERSON><PERSON> <PERSON>far<PERSON>", "item.iceandfire.sheep_helmet": "<PERSON><PERSON>farce de <PERSON>ha", "item.iceandfire.sheep_leggings": "Calças de Disfarce de Ovelha", "item.iceandfire.shiny_scales": "<PERSON><PERSON><PERSON>", "item.iceandfire.sickly_dragon_meal": "Comida de Dragão Doentia", "item.iceandfire.sickly_dragon_meal.desc_0": "Atrofia o crescimento do dragão", "item.iceandfire.silver_axe": "<PERSON><PERSON><PERSON>", "item.iceandfire.silver_hoe": "Enxada de Prata", "item.iceandfire.silver_ingot": "Lingote de Prata", "item.iceandfire.silver_nugget": "<PERSON><PERSON><PERSON>", "item.iceandfire.silver_pickaxe": "Picareta de Prata", "item.iceandfire.silver_shovel": "Pá de Prata", "item.iceandfire.silver_sword": "Espada de Prata", "item.iceandfire.siren_flute": "<PERSON><PERSON><PERSON>", "item.iceandfire.siren_flute.desc_0": "Faz os alvos se apaixonarem por 10 segundos", "item.iceandfire.siren_flute.desc_1": "Mobs apaixonados não poderão atacar", "item.iceandfire.siren_tear": "Lágrima de Sereia", "item.iceandfire.siren_tear.desc_0": "§6Drop raro§r", "item.iceandfire.spawn_egg_amphithere": "<PERSON>vo de Spawn de Anfitérion", "item.iceandfire.spawn_egg_cockatrice": "Ovo de Spawn de Cocatrice", "item.iceandfire.spawn_egg_cyclops": "Ovo de Spawn de Ciclope", "item.iceandfire.spawn_egg_death_worm": "<PERSON><PERSON> de Spawn de Verme da Morte", "item.iceandfire.spawn_egg_dread_beast": "Ovo de Spawn de Besta Sombria", "item.iceandfire.spawn_egg_dread_ghoul": "Ovo de Spawn de Carniçal Sombrio", "item.iceandfire.spawn_egg_dread_horse": "Ovo de Spawn de Cavalo do Cavaleiro Sombrio", "item.iceandfire.spawn_egg_dread_knight": "Ovo de Spawn de Cavaleiro Sombrio", "item.iceandfire.spawn_egg_dread_scuttler": "Ovo de Spawn de Rastejador So<PERSON>rio", "item.iceandfire.spawn_egg_dread_thrall": "Ovo de Spawn de Servo Sombrio", "item.iceandfire.spawn_egg_fire_dragon": "Ovo de Spawn de Dragão de Fogo", "item.iceandfire.spawn_egg_ghost": "Ovo de Spawn de Fantasma", "item.iceandfire.spawn_egg_gorgon": "Ovo de Spawn de Górgona", "item.iceandfire.spawn_egg_hippocampus": "Ovo de Spawn de Hipocampo", "item.iceandfire.spawn_egg_hippogryph": "Ovo de Spawn de Hipogrifo", "item.iceandfire.spawn_egg_hydra": "<PERSON>vo de Spawn de Hidra", "item.iceandfire.spawn_egg_ice_dragon": "Ovo de Spawn de Dragão de Gelo", "item.iceandfire.spawn_egg_lich": "Ovo de Spawn de Lich Sombrio", "item.iceandfire.spawn_egg_lightning_dragon": "Ovo de Spawn de Dragão do Raio", "item.iceandfire.spawn_egg_pixie": "Ovo de Spawn de Pixie", "item.iceandfire.spawn_egg_sea_serpent": "<PERSON><PERSON> de <PERSON> de Serpente <PERSON>", "item.iceandfire.spawn_egg_siren": "Ovo de Spawn de Sereia", "item.iceandfire.spawn_egg_stymphalian_bird": "Ovo de Spawn de Pássaro <PERSON>", "item.iceandfire.spawn_egg_troll": "Ovo de Spawn de Troll", "item.iceandfire.stone_statue": "Estátua de Pedra", "item.iceandfire.stymphalian_arrow": "<PERSON><PERSON><PERSON>", "item.iceandfire.stymphalian_arrow.desc": "A haste metálica permite que a flecha voe como um pássaro.", "item.iceandfire.stymphalian_bird_dagger": "Adaga de Pássaro <PERSON>", "item.iceandfire.stymphalian_bird_dagger.desc_0": "Velocidade de ataque extremamente rápida", "item.iceandfire.stymphalian_bird_feather": "Pena de Pássaro <PERSON>", "item.iceandfire.stymphalian_feather_bundle": "Feixe de Penas Estinfalianas", "item.iceandfire.stymphalian_feather_bundle.desc_0": "Lança penas afiadas em 8 direções ao redor do usuário", "item.iceandfire.stymphalian_skull": "Crânio de Pássaro <PERSON>", "item.iceandfire.stymphalian_skull.jei_desc": "Pode ser colocado no chão ou em paredes como um troféu.", "item.iceandfire.summoning_crystal.bound": "Vinculado a %s", "item.iceandfire.summoning_crystal.desc_0": "Clique com o botão direito em um dragão para vinculá-lo", "item.iceandfire.summoning_crystal.desc_1": "Use quando vinculado para teleportar o dragão", "item.iceandfire.summoning_crystal_fire": "Cristal de Invocação de Dragão de Fogo", "item.iceandfire.summoning_crystal_ice": "Cristal de Invocação de Dragão de Gelo", "item.iceandfire.summoning_crystal_lightning": "Cristal de Invocação de Dragão do Raio", "item.iceandfire.tide_trident": "Tridente da Maré", "item.iceandfire.tide_trident.desc_0": "Arma tridente muito forte", "item.iceandfire.tide_trident.desc_1": "<PERSON><PERSON><PERSON> m<PERSON> inimigos", "item.iceandfire.tide_trident_inventory": "Tridente da Maré", "item.iceandfire.tooltip.require.delight": "Este item requer o Farmer's Delight para ser criado", "item.iceandfire.troll_leather_armor_boots.desc": "-10%% <PERSON><PERSON>", "item.iceandfire.troll_leather_armor_chestplate.desc": "-30%% <PERSON><PERSON>", "item.iceandfire.troll_leather_armor_helmet.desc": "-10%% <PERSON><PERSON>", "item.iceandfire.troll_leather_armor_leggings.desc": "-20%% <PERSON><PERSON>", "item.iceandfire.troll_leather_forest": "<PERSON><PERSON> Troll da Floresta", "item.iceandfire.troll_leather_frost": "<PERSON>uro de Troll do Gelo", "item.iceandfire.troll_leather_mountain": "<PERSON><PERSON> Mont<PERSON>ha", "item.iceandfire.troll_skull": "Cr<PERSON><PERSON>", "item.iceandfire.troll_skull.jei_desc": "Pode ser colocado no chão ou em paredes como um troféu.", "item.iceandfire.troll_tusk": "Presa de Troll", "item.iceandfire.troll_weapon_axe": "<PERSON><PERSON><PERSON>", "item.iceandfire.troll_weapon_column": "Coluna de Pedra de Troll", "item.iceandfire.troll_weapon_column_forest": "Coluna Musgosa de Troll", "item.iceandfire.troll_weapon_column_frost": "Coluna Congelada de Troll", "item.iceandfire.troll_weapon_hammer": "<PERSON><PERSON>", "item.iceandfire.troll_weapon_trunk": "Tronco de Árvore de Troll", "item.iceandfire.troll_weapon_trunk_frost": "Tronco de Árvore Congelado de Troll", "item.iceandfire.weezer_blue_album": "Weezer", "item.iceandfire.weezer_blue_album.desc_0": "Weezer", "item.iceandfire.wither_shard": "Fragmento de Osso de Wither", "item.iceandfire.witherbone": "<PERSON><PERSON>", "itemGroup.iceandfire.blocks": "Ice And Fire | Blocos", "itemGroup.iceandfire.items": "Ice And Fire | Itens", "jei.iceandfire.dragon_forge_fire": "Forja de Dragão de Fogo", "jei.iceandfire.dragon_forge_ice": "Forja de Dragão de Gelo", "jei.iceandfire.dragon_forge_lightning": "Forja de Dragão do Raio", "key.dragon_change_view": "Mudar câmera em 3ª pessoa para o dragão", "key.dragon_down": "Dragão para Baixo", "key.dragon_fireAttack": "Sopro de Dragão", "key.dragon_strike": "Ataque do Dragão", "lectern.nopages": "Nenhuma nova informação pode ser adicionada.", "material.amphithere_feather": "<PERSON><PERSON>", "material.dragonbone": "<PERSON><PERSON> de Dragão", "material.dragonsteel_fire": "Aço de Dragão de Fogo", "material.dragonsteel_ice": "Aço de Dragão de Gelo", "material.dragonsteel_lightning": "Aço de Dragão do Raio", "material.stymph_feather": "Pena de Pássaro <PERSON>", "material.weezer": "Weezer", "message.iceandfire.dragonFollow": "Este dragão agora está Seguindo.", "message.iceandfire.dragonFollowName": "agora est<PERSON>.", "message.iceandfire.dragonGrown": "Este dragão cresceu para o Estágio", "message.iceandfire.dragonGrownEnd": "!", "message.iceandfire.dragonGrownName": "cresceu para o Estágio", "message.iceandfire.dragonSit": "Este dragão agora está Parado.", "message.iceandfire.dragonSitName": "agora está <PERSON>do.", "message.iceandfire.dragonSleep": "Este dragão agora está Dormindo.", "message.iceandfire.dragonSleepName": "agora está Dormindo.", "message.iceandfire.dragonTeleport": "Este dragão foi teleportado.", "message.iceandfire.dragonWander": "Este dragão agora está Vagando.", "message.iceandfire.dragonWanderName": "agora está Vagando.", "message.iceandfire.knownAs": "O dragão conhecido como", "message.iceandfire.noDragonTeleport": "§cNão foi possível encontrar um dragão vinculado a este cristal.", "modifier.antigravity": "Antigravidade", "modifier.antigravity.desc": "Avante!§r\\nA flecha manterá sua altura e não será afetada pela gravidade durante o voo. Onde você mirar, ela voa.", "modifier.arrow_knockback": "Repulsão Voadora", "modifier.arrow_knockback.desc": "Para o alto e avante!§r\\nA flecha infligirá forte repulsão ao atingir uma entidade.", "modifier.flame": "Inferno", "modifier.flame.desc": "Queime, baby, queime!§r\\nIncendeia inimigos por 10 segundos e causa dano adicional de chama de dragão.", "modifier.flame2": "Inferno II", "modifier.flame2.desc": "Queime, baby, queime!§r\\nIncendeia inimigos por 15 segundos com repulsão adicional e causa dano extra de chama de dragão.", "modifier.fractured2": "Fraturado II", "modifier.fractured2.desc": "Machuca!§r\\nO dano de suas ferramentas é aumentado.", "modifier.frost": "<PERSON><PERSON><PERSON>", "modifier.frost.desc": "<PERSON><PERSON>, gelo, baby!§r\\nCongela inimigos em um bloco de gelo por 10 segundos e causa dano adicional de gelo de dragão.", "modifier.frost2": "Nevasca II", "modifier.frost2.desc": "<PERSON><PERSON>, gelo, baby!§r\\nCongela inimigos em um bloco de gelo por 15 segundos com repulsão adicional e causa dano extra de gelo de dragão.", "modifier.hive_defender": "<PERSON><PERSON><PERSON> da Colmeia", "modifier.hive_defender.desc": "Pela Rainha e pela Colônia!§r\\nCausa +8 de dano extra contra vermes da morte e +4 de dano contra não-artrópodes.", "modifier.in_the_garage": "Na Garagem", "modifier.in_the_garage.desc": "\"Na garagem onde eu pertenço...\"§r\\nCausa 5 de dano adicional quando o jogador não está sob a luz direta do sol.", "modifier.splintering2": "Estilhaçamento II", "modifier.splintering2.desc": "Uma lembrança para seus inimigos!§r\\nAtinga-os mais para causar mais dano.", "modifier.splitting2": "Divisão II", "modifier.splitting2.desc": "Três por um!§r\\nA aceleração súbita ao soltar uma flecha faz com que ela se divida em três.", "modifier.surf_wax_america": "Cera de Surf América", "modifier.surf_wax_america.desc": "\"Você leva seu carro para o trabalho, eu levo minha prancha...:\"§r\\nCausa 5 de dano adicional quando os jogadores estão montados.", "modifier.sweater_song": "<PERSON><PERSON><PERSON>", "modifier.sweater_song.desc": "\"Se você quer destruir meu suéter...\"§r\\nAlvos usando armadura têm 30% de chance de terem sua armadura removida por um acerto crítico.", "screen.iceandfire.client.title": "Ice And Fire Configurações do Cliente", "screen.iceandfire.common.title": "Ice And Fire Configurações Comuns", "sea_serpent.blue": "Azul", "sea_serpent.bronze": "Bronze", "sea_serpent.deepblue": "Azul Profundo", "sea_serpent.green": "Verde", "sea_serpent.purple": "Roxo", "sea_serpent.red": "Vermelho", "sea_serpent.teal": "Verde-azulado", "silvertools.hurt": "+2 de dano contra mortos-vivos", "tc.aspect.draco": "Dragão", "tc.aspect.mythus": "Criatura Mitológica, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Criatura Assustadora", "text.iceandfire.not_enable": "Ative este recurso na configuração primeiro!", "warning.iceandfire.dreadland.not_complete": "As Terras Sombrias ainda não estão completas. Por favor, não reporte bugs de perda de conteúdo!"}