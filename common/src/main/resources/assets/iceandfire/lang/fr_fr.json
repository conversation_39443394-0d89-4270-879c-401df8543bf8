{"itemGroup.iceandfire.items": "Objets de Ice And Fire", "itemGroup.iceandfire.blocks": "Blocs de Ice And Fire", "iceandfire.empty": "Vide", "block.iceandfire.lectern": "<PERSON><PERSON><PERSON><PERSON>", "block.iceandfire.podium": "Podium", "block.iceandfire.podium_oak": "Podium en bois de chêne", "block.iceandfire.podium_spruce": "Podium en bois de sapin", "block.iceandfire.podium_birch": "Podium en bois de bouleau", "block.iceandfire.podium_jungle": "Podium en bois d'acajou", "block.iceandfire.podium_acacia": "Podium en bois d'acacia", "block.iceandfire.podium_dark_oak": "Podium en bois de chêne noir", "block.iceandfire.fire_lily": "<PERSON><PERSON> <PERSON>", "block.iceandfire.frost_lily": "<PERSON><PERSON>", "block.iceandfire.lightning_lily": "Lis d'Électricité", "block.iceandfire.gold_pile": "<PERSON><PERSON> d'<PERSON>", "block.iceandfire.silver_pile": "<PERSON><PERSON>", "block.iceandfire.copper_pile": "<PERSON><PERSON>", "block.iceandfire.silver_ore": "Minerai <PERSON>", "block.iceandfire.deepslate_silver_ore": "Minerai d'Argent des abîmes", "block.iceandfire.raw_silver_block": "Bloc d'Argent brut", "block.iceandfire.sapphire_ore": "Minerai de Saphir", "block.iceandfire.silver_block": "Bloc d'Argent", "block.iceandfire.sapphire_block": "<PERSON> de Saphir", "block.iceandfire.chared_dirt": "Terre Carbonisée", "block.iceandfire.chared_grass": "Bloc d'Herbe Carbonisé", "block.iceandfire.chared_stone": "Roche <PERSON>", "block.iceandfire.chared_cobblestone": "<PERSON>", "block.iceandfire.chared_gravel": "<PERSON><PERSON><PERSON>", "block.iceandfire.chared_dirt_path": "<PERSON><PERSON><PERSON> Carbon<PERSON>", "block.iceandfire.ash": "Cendres", "block.iceandfire.frozen_dirt": "<PERSON><PERSON> G<PERSON>", "block.iceandfire.frozen_grass": "Bloc d'Herbe Gelé", "block.iceandfire.frozen_stone": "<PERSON>", "block.iceandfire.frozen_cobblestone": "<PERSON>", "block.iceandfire.frozen_gravel": "<PERSON><PERSON><PERSON>", "block.iceandfire.frozen_dirt_path": "<PERSON><PERSON><PERSON> <PERSON>", "block.iceandfire.frozen_splinters": "Éclats Gelés", "block.iceandfire.dragon_ice": "<PERSON><PERSON> de <PERSON>", "block.iceandfire.dragon_ice_spikes": "Pointes de Glace de Dragon", "block.iceandfire.crackled_dirt": "<PERSON><PERSON>", "block.iceandfire.crackled_grass": "Bloc d'Herbe <PERSON>", "block.iceandfire.crackled_stone": "<PERSON>", "block.iceandfire.crackled_cobblestone": "<PERSON>", "block.iceandfire.crackled_gravel": "<PERSON><PERSON><PERSON>", "block.iceandfire.crackled_dirt_path": "<PERSON><PERSON><PERSON> <PERSON>", "block.iceandfire.egginice": "<PERSON><PERSON><PERSON> de dragon de glace", "block.iceandfire.pixie_house_mushroom_brown": "<PERSON>son de Fée en Champignon Marron", "block.iceandfire.pixie_house_mushroom_red": "Maison de Fée en Champignon Rouge", "block.iceandfire.pixie_house_birch": "<PERSON>son de Fée en Bois de Bouleau", "block.iceandfire.pixie_house_oak": "<PERSON>son de Fée en Bois de Chêne", "block.iceandfire.pixie_house_dark_oak": "<PERSON>son de Fée en Bois de Chêne Noir", "block.iceandfire.pixie_house_spruce": "<PERSON>son de Fée en Bois de Sapin", "block.iceandfire.pixie_jar_empty": "Bocal", "block.iceandfire.pixie_jar_0": "Bocal à Fée Rose", "block.iceandfire.pixie_jar_1": "Bocal à Fée Violette", "block.iceandfire.pixie_jar_2": "Bocal à Fée Bleue", "block.iceandfire.pixie_jar_3": "Bocal à Fée Verte", "block.iceandfire.pixie_jar_4": "Bocal à Fée Jaune", "block.iceandfire.nest": "<PERSON><PERSON>", "block.iceandfire.dragonscale_red": "Bloc d'Écailles de Dragon", "block.iceandfire.dragonscale_green": "Bloc d'Écailles de Dragon", "block.iceandfire.dragonscale_bronze": "Bloc d'Écailles de Dragon", "block.iceandfire.dragonscale_gray": "Bloc d'Écailles de Dragon", "block.iceandfire.dragonscale_blue": "Bloc d'Écailles de Dragon", "block.iceandfire.dragonscale_white": "Bloc d'Écailles de Dragon", "block.iceandfire.dragonscale_sapphire": "Bloc d'Écailles de Dragon", "block.iceandfire.dragonscale_silver": "Bloc d'Écailles de Dragon", "block.iceandfire.dragonscale_electric": "Bloc d'Écailles de Dragon", "block.iceandfire.dragonscale_amethyst": "Bloc d'Écailles de Dragon", "block.iceandfire.dragonscale_copper": "Bloc d'Écailles de Dragon", "block.iceandfire.dragonscale_black": "Bloc d'Écailles de Dragon", "block.iceandfire.dragon_bone_block": "Bloc d'Os de Dragon", "block.iceandfire.dragon_bone_wall": "<PERSON><PERSON> <PERSON><PERSON>", "block.iceandfire.sea_serpent_scale_block": "Bloc d'Écailles de Serpent des Mers", "block.iceandfire.sea_serpent_scale_block_red": "Bloc d'Écailles de Serpent des Mers", "block.iceandfire.sea_serpent_scale_block_green": "Bloc d'Écailles de Serpent des Mers", "block.iceandfire.sea_serpent_scale_block_deepblue": "Bloc d'Écailles de Serpent des Mers", "block.iceandfire.sea_serpent_scale_block_blue": "Bloc d'Écailles de Serpent des Mers", "block.iceandfire.sea_serpent_scale_block_bronze": "Bloc d'Écailles de Serpent des Mers", "block.iceandfire.sea_serpent_scale_block_purple": "Bloc d'Écailles de Serpent des Mers", "block.iceandfire.sea_serpent_scale_block_teal": "Bloc d'Écailles de Serpent des Mers", "block.iceandfire.dragonforge_fire_brick": "Briques de Forge de Dragon de Feu", "block.iceandfire.dragonforge_fire_core": "Coeur de Forge de Dragon de Feu", "block.iceandfire.dragonforge_fire_core_disabled": "Coeur de Forge de Dragon de Feu", "block.iceandfire.dragonforge_fire_input": "Ouverture de Forge de Dragon de Feu", "block.iceandfire.dragonforge_ice_brick": "Briques de Forge de Dragon de Glace", "block.iceandfire.dragonforge_ice_core": "Coeur de Forge de Dragon <PERSON>lace", "block.iceandfire.dragonforge_ice_core_disabled": "Coeur de Forge de Dragon <PERSON>lace", "block.iceandfire.dragonforge_ice_input": "Ouverture de Forge de Dragon de Glace", "block.iceandfire.dragonforge_lightning_brick": "Briques de Forge de Dragon Électrique", "block.iceandfire.dragonforge_lightning_core": "Coeur de Forge de Dragon Électrique", "block.iceandfire.dragonforge_lightning_core_disabled": "Coeur de Forge de Dragon Électrique", "block.iceandfire.dragonforge_lightning_input": "Ouverture de Forge de Dragon Électrique", "block.iceandfire.dragonsteel_fire_block": "Bloc d'Acier de Dragon de Feu", "block.iceandfire.dragonsteel_ice_block": "Bloc d'Acier de Dragon de Glace", "block.iceandfire.dragonsteel_lightning_block": "Bloc d'Acier de Dragon Électrique", "block.iceandfire.dread_stone": "<PERSON><PERSON><PERSON><PERSON>", "block.iceandfire.dread_stone_bricks": "Briqueffroie", "block.iceandfire.dread_stone_bricks_chiseled": "Briqueffroie carrel<PERSON>", "block.iceandfire.dread_stone_bricks_cracked": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> c<PERSON>", "block.iceandfire.dread_stone_bricks_mossy": "<PERSON><PERSON>queff<PERSON><PERSON> moussue", "block.iceandfire.dread_stone_face": "<PERSON><PERSON><PERSON>e sculptée en crâne", "block.iceandfire.dread_stone_tile": "Rocheffroie lisse", "block.iceandfire.dread_torch": "Torcheffroie", "block.iceandfire.dread_torch_wall": "Torcheffroie", "block.iceandfire.burnt_torch": "Torche brûlée", "block.iceandfire.burnt_torch_wall": "Torche brûlée", "block.iceandfire.dread_stone_slab": "<PERSON>le de briqueffroie", "block.iceandfire.dread_stone_stairs": "Escalier en briqueffroie", "block.iceandfire.dreadwood_log": "Bûche de boiveffroie", "block.iceandfire.dreadwood_planks": "Planche de boiveffroie", "block.iceandfire.dreadwood_planks_lock": "Sérure en boiveffroie", "block.iceandfire.dread_portal": "Portail des Terre d'Effroies", "block.iceandfire.dread_spawner": "Générateur de monstreffroie", "block.iceandfire.ghost_chest": "<PERSON><PERSON><PERSON>", "block.iceandfire.graveyard_soil": "Graveyard Soil", "block.iceandfire.graveyard_soil.desc": "Fait apparaître des Fantômes la nuit", "item.iceandfire.bestiary": "Bestiaire", "item.iceandfire.manuscript": "Manus<PERSON><PERSON>", "item.iceandfire.sapphire_gem": "<PERSON><PERSON><PERSON>", "item.iceandfire.amethyst_gem": "<PERSON><PERSON><PERSON><PERSON>", "item.iceandfire.silver_ingot": "<PERSON><PERSON>", "item.iceandfire.raw_silver": "Argent brut", "item.iceandfire.silver_nugget": "<PERSON><PERSON><PERSON><PERSON>", "item.iceandfire.copper_nugget": "<PERSON><PERSON><PERSON><PERSON>", "item.iceandfire.armor_silver_metal_helmet": "Casque en Argent", "item.iceandfire.armor_silver_metal_chestplate": "Plastron en Argent", "item.iceandfire.armor_silver_metal_leggings": "Jambière en Argent", "item.iceandfire.armor_silver_metal_boots": "Bottes en Argent", "item.iceandfire.silver_pickaxe": "Pioche en Argent", "item.iceandfire.silver_sword": "<PERSON><PERSON><PERSON> en <PERSON>rgent", "item.iceandfire.silver_axe": "Hache en Argent", "item.iceandfire.silver_shovel": "<PERSON><PERSON> en <PERSON>rgent", "item.iceandfire.silver_hoe": "<PERSON><PERSON> en Argent", "item.iceandfire.armor_copper_metal_helmet": "Casque en Cuivre", "item.iceandfire.armor_copper_metal_chestplate": "Plaston en Cuivre", "item.iceandfire.armor_copper_metal_leggings": "Jambière en Cuivre", "item.iceandfire.armor_copper_metal_boots": "Bottes en Cuivre", "item.iceandfire.copper_pickaxe": "Pioche en Cuivre", "item.iceandfire.copper_sword": "Épée en Cuivre", "item.iceandfire.copper_axe": "Hache en Cuivre", "item.iceandfire.copper_shovel": "<PERSON>elle en Cuivre", "item.iceandfire.copper_hoe": "<PERSON>ue en Cuivre", "item.iceandfire.fire_stew": "Mélange de Lis de Feu", "item.iceandfire.frost_stew": "Mélange de Lis du Froid", "item.iceandfire.lightning_stew": "Mélange de Lis Électrique", "item.iceandfire.dragonegg": "<PERSON><PERSON><PERSON>", "item.iceandfire.dragonscales": "Écailles de Dragon", "item.dragonscales_armor.desc": "Augmente votre protection contre les crachas de dragon", "item.iceandfire.dragon_helmet": "Casque en Écailles de Dragon", "item.iceandfire.dragon_chestplate": "Plastron en Écailles de Dragon", "item.iceandfire.dragon_leggings": "Jambière en Écailles de Dragon", "item.iceandfire.dragon_boots": "Bottes en Écailles de Dragon", "item.iceandfire.dragonbone": "<PERSON><PERSON> <PERSON>", "item.iceandfire.witherbone": "<PERSON><PERSON>", "item.iceandfire.fishing_spear": "<PERSON>", "item.iceandfire.wither_shard": "Éclat <PERSON>'<PERSON>", "item.iceandfire.dragonbone_sword": "Épée en Os de Dragon", "item.iceandfire.dragonbone_shovel": "Pelle en Os de Dragon", "item.iceandfire.dragonbone_pickaxe": "Pioche en Os de Dragon", "item.iceandfire.dragonbone_axe": "Hache en en Os de Dragon", "item.iceandfire.dragonbone_hoe": "Houe en Os de Dragon", "item.iceandfire.dragonbone_sword_fire": "<PERSON><PERSON><PERSON> en Os de Dragon", "item.iceandfire.dragonbone_sword_ice": "<PERSON><PERSON><PERSON> en Os de Dragon", "item.iceandfire.dragonbone_sword_lightning": "É<PERSON><PERSON> Électrifié en Os de Dragon", "item.iceandfire.dragonbone_arrow": "Flèche en Os de Dragon", "item.iceandfire.dragonbone_bow": "Arc en Os de Dragon", "item.iceandfire.dragon_skull_fire": "<PERSON>", "item.iceandfire.dragon_skull_ice": "<PERSON>", "item.iceandfire.dragon_skull_lightning": "<PERSON>", "item.iceandfire.dragonarmor_iron": "<PERSON>ure de Dragon en fer", "item.iceandfire.dragonarmor_gold": "Armure de Dragon en or", "item.iceandfire.dragonarmor_diamond": "<PERSON>ure de Dragon en diamant", "item.iceandfire.dragonarmor_silver": "Armure de Dragon en argent", "item.iceandfire.dragonarmor_copper": "Armure de Dragon en cuivre", "item.iceandfire.dragon_meal": "Repas de Dragon", "item.iceandfire.fire_dragon_flesh": "<PERSON> <PERSON>", "item.iceandfire.ice_dragon_flesh": "<PERSON> <PERSON>", "item.iceandfire.lightning_dragon_flesh": "Chair de Dragon Électrique", "item.iceandfire.fire_dragon_heart": "Coeur de Dragon <PERSON>u", "item.iceandfire.ice_dragon_heart": "<PERSON><PERSON> <PERSON>", "item.iceandfire.lightning_dragon_heart": "Coeur de Dragon Électrique", "item.iceandfire.fire_dragon_blood": "Sang de Dragon de Feu", "item.iceandfire.ice_dragon_blood": "Sang de Dragon de <PERSON>lace", "item.iceandfire.lightning_dragon_blood": "Sang de Dragon Électrique", "item.iceandfire.dragon_stick": "Bâton de Commandement de Dragon", "item.iceandfire.dragon_horn": "<PERSON><PERSON>", "item.iceandfire.dragon_horn_fire": "<PERSON><PERSON>", "item.iceandfire.dragon_horn_ice": "<PERSON><PERSON>", "item.iceandfire.dragon_flute": "Flute en Os de Dragon", "item.iceandfire.hippogryph_egg": "<PERSON><PERSON><PERSON>", "item.iceandfire.iron_hippogryph_armor": "<PERSON>ure d'Hippogriffe en fer", "item.iceandfire.gold_hippogryph_armor": "Armure d'Hippogriffe en or", "item.iceandfire.diamond_hippogryph_armor": "<PERSON>ure d'Hippogriffe en fiamant", "item.iceandfire.netherite_hippogryph_armor": "<PERSON><PERSON> d'Hippogriffe en netherite", "item.iceandfire.gorgon_head": "<PERSON><PERSON><PERSON>", "item.iceandfire.stone_statue": "Statue en Roche", "item.iceandfire.blindfold": "Bandeau", "item.iceandfire.pixie_dust": "<PERSON><PERSON><PERSON>", "item.iceandfire.ambrosia": "Ambroisie", "item.iceandfire.sheep_helmet": "Casque de Déguisement de Mouton", "item.iceandfire.sheep_chestplate": "Plastron de Déguisement de Mouton", "item.iceandfire.sheep_leggings": "Jambière de Déguisement de Mouton", "item.iceandfire.sheep_boots": "Bottes de Déguisement de Mouton", "item.iceandfire.shiny_scales": "Écailles Brillantes", "item.iceandfire.earplugs": "Bouchon d'Oreille", "item.iceandfire.air_pods": "AirPods Apple", "item.iceandfire.air_pods.desc": "Ô mon dieu ! <PERSON> a des AirPods, il ne peut pas nous entendre !", "item.iceandfire.deathworm_chitin_yellow": "Chitine Foncée de Ver des sables", "item.iceandfire.deathworm_chitin_white": "<PERSON><PERSON> V<PERSON> des sables", "item.iceandfire.deathworm_chitin_red": "Chitine Rouge de Ver des sables", "item.iceandfire.deathworm_yellow_helmet": "Casque en Chitine Foncée de Ver des sables", "item.iceandfire.deathworm_yellow_chestplate": "Plastron en Chitine Foncée de Ver des sables", "item.iceandfire.deathworm_yellow_leggings": "Jambière en Chitine Foncée de Ver des sables", "item.iceandfire.deathworm_yellow_boots": "Bottes en Chitine Foncée de Ver des sables", "item.iceandfire.deathworm_white_helmet": "Casque en Chitine Blanche de Ver des sables", "item.iceandfire.deathworm_white_chestplate": "Plastron en Chitine Blanche de Ver des sables", "item.iceandfire.deathworm_white_leggings": "Jambière en Chitine Blanche de Ver des sables", "item.iceandfire.deathworm_white_boots": "Bottes en Chitine Blanche de Ver des sables", "item.iceandfire.deathworm_red_helmet": "Casque en Chitine Rouge de Ver des sables", "item.iceandfire.deathworm_red_chestplate": "Plastron en Chitine Rouge de Ver des sables", "item.iceandfire.deathworm_red_leggings": "Jambière en Chitine Rouge de Ver des sables", "item.iceandfire.deathworm_red_boots": "Bottes en Chitine Rouge de Ver des sables", "item.iceandfire.deathworm_egg": "<PERSON><PERSON><PERSON> des sables", "item.iceandfire.deathworm_egg_giant": "<PERSON><PERSON><PERSON> sables", "item.iceandfire.rotten_egg": "<PERSON><PERSON><PERSON>", "item.iceandfire.stymphalian_bird_feather": "Plume d'Oiseau du Lac Stymphale", "item.iceandfire.stymphalian_arrow": "Flèche d'Oiseau du Lac Stymphale", "item.iceandfire.stymphalian_arrow.desc": "L'étirement métallique permet à la flèche de s'élever comme un oiseau.", "item.iceandfire.troll_weapon_axe": "Hache du Troll", "item.iceandfire.troll_weapon_column": "Colonne en Roche du Troll", "item.iceandfire.troll_weapon_column_forest": "<PERSON><PERSON><PERSON>", "item.iceandfire.troll_weapon_column_frost": "<PERSON><PERSON><PERSON>", "item.iceandfire.troll_weapon_hammer": "Marteau du Troll", "item.iceandfire.troll_weapon_trunk": "Tronc d'Arbre du Troll", "item.iceandfire.troll_weapon_trunk_frost": "Tronc d'Arbre Gelé <PERSON>", "item.iceandfire.troll_tusk": "Défense du Troll", "item.iceandfire.troll_leather_forest": "<PERSON><PERSON><PERSON> <PERSON> la Forêt", "item.iceandfire.troll_leather_frost": "<PERSON><PERSON><PERSON> <PERSON>", "item.iceandfire.troll_leather_mountain": "<PERSON><PERSON><PERSON> <PERSON> la Montagne", "item.iceandfire.forest_troll_leather_helmet": "Casque en Cuir du Troll de la Forêt", "item.iceandfire.forest_troll_leather_chestplate": "Plastron en Cuir du Troll de la Forêt", "item.iceandfire.forest_troll_leather_leggings": "Jambière en Cuir du Troll de la Forêt", "item.iceandfire.forest_troll_leather_boots": "Bottes en Cuir du Troll de la Forêt", "item.iceandfire.frost_troll_leather_helmet": "Casque en Cuir du Troll du Froid", "item.iceandfire.frost_troll_leather_chestplate": "Plastron en Cuir du Troll du Froid", "item.iceandfire.frost_troll_leather_leggings": "Jambière en Cuir du Troll du Froid", "item.iceandfire.frost_troll_leather_boots": "Bottes en Cuir du Troll du Froid", "item.iceandfire.mountain_troll_leather_helmet": "Casque en Cuir du Troll de la Montagne", "item.iceandfire.mountain_troll_leather_chestplate": "Plastron en Cuir du Troll de la Montagne", "item.iceandfire.mountain_troll_leather_leggings": "Jambière en Cuir du Troll de la Montagne", "item.iceandfire.mountain_troll_leather_boots": "Bottes en Cuir du Troll de la Montagne", "item.iceandfire.troll_leather_armor_helmet.desc": "-10%% de Dégâts de Projectile", "item.iceandfire.troll_leather_armor_chestplate.desc": "-30%% Dégâts de Projectile", "item.iceandfire.troll_leather_armor_leggings.desc": "-20%% Dégâts de Projectile", "item.iceandfire.troll_leather_armor_boots.desc": "-10%% Dégâts de Projectile", "item.iceandfire.sickly_dragon_meal": "<PERSON><PERSON>", "item.iceandfire.sickly_dragon_meal.desc_0": "Retarde la Croissance du Dragon", "item.iceandfire.amphithere_feather": "Plume d'Am<PERSON>ptère", "item.iceandfire.amphithere_arrow": "Flèche d'Amphiptère", "item.iceandfire.amphithere_arrow.desc": "Repousse tous les monstres avec la puissance des ailes d'un amphiptère !", "item.iceandfire.sea_serpent_fang": "<PERSON><PERSON><PERSON>", "item.iceandfire.sea_serpent_scales_blue": "Écailles de Serpent <PERSON>", "item.iceandfire.sea_serpent_scales_bronze": "Écailles de Serpent <PERSON>", "item.iceandfire.sea_serpent_scales_deepblue": "Écailles de Serpent <PERSON>", "item.iceandfire.sea_serpent_scales_green": "Écailles de Serpent <PERSON>", "item.iceandfire.sea_serpent_scales_purple": "Écailles de Serpent <PERSON>", "item.iceandfire.sea_serpent_scales_red": "Écailles de Serpent <PERSON>", "item.iceandfire.sea_serpent_scales_teal": "Écailles de Serpent <PERSON>", "item.iceandfire.sea_serpent_helmet": "Casque de Gardien de la Marée", "item.iceandfire.sea_serpent_chestplate": "Plastron de Gardien de la Marée", "item.iceandfire.sea_serpent_leggings": "Jambière de Gardien de la Marée", "item.iceandfire.sea_serpent_boots": "Bottes de Gardien de la Marée", "item.iceandfire.sea_serpent_arrow": "Flèche de la Marée", "item.iceandfire.sea_serpent_arrow.desc": "Non-inondé par l'eau !", "item.iceandfire.sea_serpent_armor.desc_0": "Fournit de la Respiration Aquatique", "item.iceandfire.sea_serpent_armor.desc_1": "<PERSON><PERSON><PERSON> de la Force lorsque vous êtes trempé, augmente son intensité avec l'armure complète", "item.iceandfire.chain": "<PERSON><PERSON><PERSON>", "item.iceandfire.chain.desc_0": "<PERSON><PERSON><PERSON><PERSON> à la laisse, peut être liée à des murs", "item.iceandfire.chain.desc_1": "Ne peut pas être brisée, peut être utilisée sur la plupart des mobs", "item.iceandfire.chain_sticky": "<PERSON><PERSON>ne en Fer Collante", "item.iceandfire.chain_sticky.desc_2": "Utilisé pour connecter deux mobs en les enchaînants", "item.iceandfire.chain_sticky.desc_3": "Perd son pouvoir collant après l'utilisation", "item.iceandfire.creative_dragon_meal": "Repas de Dragon Créatif", "item.iceandfire.creative_dragon_meal.desc_0": "Apprivoiser n'importe quelle Dragon", "item.iceandfire.creative_dragon_meal.desc_1": "Seulement en Mode Créatif", "item.iceandfire.dragonsteel_fire_ingot": "Lingot d'Acier de Dragon de Feu", "item.iceandfire.dragonsteel_fire_sword": "Épée en Acier de Dragon de Feu", "item.iceandfire.dragonsteel_fire_axe": "Hache en Acier de Dragon de Feu", "item.iceandfire.dragonsteel_fire_pickaxe": "Pioche en Acier de Dragon de Feu", "item.iceandfire.dragonsteel_fire_shovel": "Pelle en Acier de Dragon de Feu", "item.iceandfire.dragonsteel_fire_hoe": "Houe en Acier de Dragon de Feu", "item.iceandfire.dragonsteel_fire_helmet": "Casque en Acier de Dragon de Feu", "item.iceandfire.dragonsteel_fire_chestplate": "Plastron en Acier de Dragon de Feu", "item.iceandfire.dragonsteel_fire_leggings": "Jambière en Acier de Dragon de Feu", "item.iceandfire.dragonsteel_fire_boots": "Bottes en Acier de Dragon de Feu", "item.iceandfire.dragonsteel_ice_ingot": "Lingot d'Acier de Dragon <PERSON>lace", "item.iceandfire.dragonsteel_ice_sword": "Épée en Acier de Dragon de Glace", "item.iceandfire.dragonsteel_ice_axe": "Hache en Acier de Dragon de Glace", "item.iceandfire.dragonsteel_ice_pickaxe": "Pioche en Acier de Dragon de Glace", "item.iceandfire.dragonsteel_ice_shovel": "Pelle en Acier de Dragon de Glace", "item.iceandfire.dragonsteel_ice_hoe": "Houe en Acier de Dragon de Glace", "item.iceandfire.dragonsteel_ice_helmet": "Casque en Acier de Dragon de Glace", "item.iceandfire.dragonsteel_ice_chestplate": "Plastron en Acier de Dragon de Glace", "item.iceandfire.dragonsteel_ice_leggings": "Jambière en Acier de Dragon de Glace", "item.iceandfire.dragonsteel_ice_boots": "Bottes en Acier de Dragon de Glace", "item.iceandfire.dragonsteel_lightning_ingot": "Lingot d'Acier de Dragon Électrique", "item.iceandfire.dragonsteel_lightning_sword": "Épée en Acier de Dragon Électrique", "item.iceandfire.dragonsteel_lightning_axe": "Hache en Acier de Dragon Électrique", "item.iceandfire.dragonsteel_lightning_pickaxe": "Pioche en Acier de Dragon Électrique", "item.iceandfire.dragonsteel_lightning_shovel": "Pelle en Acier de Dragon Électrique", "item.iceandfire.dragonsteel_lightning_hoe": "Houe en Acier de Dragon Électrique", "item.iceandfire.dragonsteel_lightning_helmet": "Casque en Acier de Dragon Électrique", "item.iceandfire.dragonsteel_lightning_chestplate": "Plastron en Acier de Dragon Électrique", "item.iceandfire.dragonsteel_lightning_leggings": "Jambière en Acier de Dragon Électrique", "item.iceandfire.dragonsteel_lightning_boots": "Bottes en Acier de Dragon Électrique", "item.iceandfire.pixie_wings": "<PERSON><PERSON>", "item.iceandfire.pixie_wings.desc_0": "§6Butin Rare§r", "item.iceandfire.legendary_weapon.desc": "§6Arme Légendaire", "item.iceandfire.deathworm_tounge": "Langue de Ver des sables", "item.iceandfire.deathworm_tounge.desc_0": "§6Butin Rare§r", "item.iceandfire.deathworm_gauntlet_yellow": "Gants du Ver des sables", "item.iceandfire.deathworm_gauntlet_white": "Gants du Ver des sables", "item.iceandfire.deathworm_gauntlet_red": "Gants du Ver des sables", "item.iceandfire.deathworm_gauntlet.desc_0": "Donne un coup de langue à la cible qui inflige 3 points de dégâts", "item.iceandfire.deathworm_gauntlet.desc_1": "Tire la cible vers le joueur", "item.iceandfire.pixie_wand": "Baguette de Fée", "item.iceandfire.pixie_wand.desc_0": "Tire une charge magique qui inflige 5 points de dégâts et fait léviter la cible.", "item.iceandfire.pixie_wand.desc_1": "Utilise la Poudre de Fée comme munition", "item.iceandfire.hippogryph_talon": "<PERSON> d'Hippogryphe", "item.iceandfire.hippogryph_talon.desc_0": "§6Butin Rare§r", "item.iceandfire.hippogryph_sword": "Épée en Talon Hippogryphe", "item.iceandfire.hippogryph_sword.desc_0": "<PERSON><PERSON><PERSON> toujours la cible, en lui infligeant des dégâts supplémentaires", "item.iceandfire.hippogryph_sword.desc_1": "Every use of it is a sweeping attack", "item.iceandfire.cyclops_eye": "<PERSON><PERSON>", "item.iceandfire.cyclops_eye.desc_0": "Arme à effet de zone", "item.iceandfire.cyclops_eye.desc_1": "Inflige une faiblesse à toutes les créatures hostiles proches lorsqu'il est tenu.", "item.iceandfire.cockatrice_eye": "<PERSON><PERSON>", "item.iceandfire.cockatrice_eye.desc_0": "§6Butin Rare§r", "item.iceandfire.cockatrice_scepter": "<PERSON><PERSON><PERSON>", "item.iceandfire.cockatrice_scepter.desc_0": "La cible reçoit wither lorsqu'elle est utilisée", "item.iceandfire.cockatrice_scepter.desc_1": "Peut cibler plusieurs entités proches", "item.iceandfire.siren_tear": "<PERSON><PERSON><PERSON>", "item.iceandfire.siren_tear.desc_0": "§6Butin Rare§r", "item.iceandfire.siren_flute": "<PERSON><PERSON><PERSON><PERSON>", "item.iceandfire.siren_flute.desc_0": "Fait tomber les cibles amoureuses pendant 10 secondes", "item.iceandfire.siren_flute.desc_1": "Les monstres amoureux ne seront pas en mesure d'attaquer", "item.iceandfire.hippocampus_fin": "<PERSON><PERSON><PERSON>", "item.iceandfire.hippocampus_fin.desc_0": "§6Butin Rare§r", "item.iceandfire.hippocampus_slapper": "Claqueur d'Hippocampus", "item.iceandfire.hippocampus_slapper.desc_0": "§dArme Comique§r", "item.iceandfire.hippocampus_slapper.desc_1": "Étourdit et ralentit les cibles ; il est assez déroutant d'être frappé par une queue de poisson.", "item.iceandfire.stymphalian_feather_bundle": "Bundle de Plumes d'Oiseaux Stymphaliens", "item.iceandfire.stymphalian_feather_bundle.desc_0": "Lance des plumes acérées dans 8 directions autour de l'utilisateur", "item.iceandfire.stymphalian_bird_dagger": "Dague d'Oiseau Stymphalien", "item.iceandfire.stymphalian_bird_dagger.desc_0": "Vitesse d'attaque extrêmement rapide", "item.iceandfire.amphithere_macuahuitl": "Macuahuitl d'Amphithère", "item.iceandfire.amphithere_macuahuitl.desc_0": "Frappe les entités en haut et en arrière", "item.iceandfire.amphithere_macuahuitl.desc_1": "Désactive les boucliers", "item.iceandfire.tide_trident": "Trident de Marée", "item.iceandfire.tide_trident_inventory": "Tide Trident", "item.iceandfire.tide_trident.desc_0": "Arme trident très puissante", "item.iceandfire.tide_trident.desc_1": "Transperce plusieurs ennemis", "item.iceandfire.hippogryph_skull": "<PERSON><PERSON><PERSON><PERSON>'Hip<PERSON>gryphe", "item.iceandfire.cyclops_skull": "Crâne de Cyclope", "item.iceandfire.cockatrice_skull": "<PERSON><PERSON><PERSON><PERSON>", "item.iceandfire.stymphalian_skull": "Crâne d'Oiseau Stymphalien", "item.iceandfire.troll_skull": "<PERSON><PERSON><PERSON><PERSON>", "item.iceandfire.amphithere_skull": "<PERSON><PERSON><PERSON><PERSON> d'Amphithère", "item.iceandfire.seaserpent_skull": "<PERSON><PERSON><PERSON><PERSON>", "item.iceandfire.hydra_skull": "Crâne d'Hydre", "item.iceandfire.dread_sword": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "item.iceandfire.dread_knight_sword": "<PERSON><PERSON><PERSON> de <PERSON>", "item.iceandfire.lich_staff": "Bâton de la Liche de l'Épouvante", "item.iceandfire.dread_queen_sword": "<PERSON><PERSON> de la Reineffroie", "item.iceandfire.dread_queen_staff": "<PERSON><PERSON><PERSON> de la Reineffroie", "item.iceandfire.weezer_blue_album": "Weezer", "item.iceandfire.weezer_blue_album.desc_0": "Weezer", "item.iceandfire.dragon_debug_stick": "Bâton de Debogage de Dragon", "item.iceandfire.dragon_debug_stick.desc_0": "A utiliser seulement pour les développeurs...", "item.iceandfire.dread_shard": "<PERSON><PERSON><PERSON><PERSON>", "item.iceandfire.dread_key": "§bClé du Maître des Tombes§r", "item.iceandfire.hydra_fang": "Croc de l'Hydre", "item.iceandfire.hydra_heart": "Coeur de l'Hydre", "item.iceandfire.cannoli": "§6Cannoli§r", "item.iceandfire.cannoli.desc": "\"V<PERSON> pouvez rencontrer des dragons ou des ruffians\"", "item.iceandfire.hydra_heart.desc_0": "Quand il est dans la barre d'outils, il donne une régénération croissante", "item.iceandfire.hydra_heart.desc_1": "en fonction de la gravité de la blessure du joueur", "item.iceandfire.hydra_arrow": "Flèche de l'Hydre", "item.iceandfire.hydra_arrow.desc": "Empoisonne et aspire la vie de la cible", "item.iceandfire.summoning_crystal_ice": "Cristal d'Invocation de Dragon de Glace", "item.iceandfire.summoning_crystal_fire": "Cristal d'Invocation de Dragon de Feu", "item.iceandfire.summoning_crystal_lightning": "Cristal d'Invocation de Dragon Électrique", "item.iceandfire.summoning_crystal.desc_0": "Cliquez avec le bouton droit de la souris sur un dragon pour le lier", "item.iceandfire.summoning_crystal.desc_1": "A utiliser lorsqu'il est lié pour téléporter le dragon", "item.iceandfire.summoning_crystal.bound": "Lié à %s", "item.iceandfire.ectoplasm": "Ectoplasme", "item.iceandfire.ghost_ingot": "Lingot Fantasmagorique", "item.iceandfire.ghost_ingot.desc_0": "§6Butin Rare§r", "item.iceandfire.ghost_sword": "Lame Fantasmatique", "item.iceandfire.ghost_sword.desc_0": "Tire une épée tournante qui traverse les blocs.", "item.iceandfire.ghost_sword.desc_1": "Le projectile de l'épée inflige des dégâts supplémentaires", "item.iceandfire.spawn_egg_fire_dragon": "Oeuf d'Apparition de Dragon de Feu", "item.iceandfire.spawn_egg_ice_dragon": "Oeuf d'Apparition de Dragon de Glace", "item.iceandfire.spawn_egg_lightning_dragon": "Oeuf d'Apparition de Dragon Électrique", "item.iceandfire.spawn_egg_hippogryph": "Oeuf d'Apparition d'Hippogryphe", "item.iceandfire.spawn_egg_gorgon": "Oeuf d'Apparition de Gorgone", "item.iceandfire.spawn_egg_pixie": "Oeuf d'Apparition de Fée", "item.iceandfire.spawn_egg_cyclops": "Oeuf d'Apparition de Cyclope", "item.iceandfire.spawn_egg_siren": "<PERSON>euf d'Apparition de Sirène", "item.iceandfire.spawn_egg_sea_serpent": "<PERSON>euf d'Apparition de Serpent <PERSON>", "item.iceandfire.spawn_egg_hippocampus": "Oeuf d'Apparition d'Hippocampus", "item.iceandfire.spawn_egg_death_worm": "Oeuf d'Apparition de Ver des sables", "item.iceandfire.spawn_egg_cockatrice": "Oeuf d'Apparition de Basilic", "item.iceandfire.spawn_egg_stymphalian_bird": "Oeuf d'Apparition d'Oiseaux Stymphalien", "item.iceandfire.spawn_egg_troll": "Oeuf d'Apparition de Troll", "item.iceandfire.spawn_egg_amphithere": "<PERSON><PERSON>f d'Apparition d'Amphithère", "item.iceandfire.spawn_egg_dread_thrall": "Oeuf d'Apparition de Squeleffroie", "item.iceandfire.spawn_egg_dread_ghoul": "Oeuf d'Apparition de Gouleffroie", "item.iceandfire.spawn_egg_dread_beast": "Oeuf d'Apparition de Bêteffroie", "item.iceandfire.spawn_egg_dread_scuttler": "Oeuf d'Apparition de Myrmexffroie", "item.iceandfire.spawn_egg_lich": "Oeuf d'Apparition de Licheffroie", "item.iceandfire.spawn_egg_dread_knight": "Oeuf d'Apparition de Chevaffroie", "item.iceandfire.spawn_egg_dread_horse": "Oeuf d'Apparition de Cheval de chevaffroie", "item.iceandfire.spawn_egg_hydra": "Oeuf d'Apparition d'Hydre", "item.iceandfire.spawn_egg_ghost": "<PERSON><PERSON>f d'Apparition de Fantôme", "item.iceandfire.banner_pattern_fire": "<PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_fire.desc": "Dragon de Feu", "item.iceandfire.banner_pattern_ice": "<PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_ice.desc": "<PERSON> de Glace", "item.iceandfire.banner_pattern_lightning": "<PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_lightning.desc": "Dragon Électrique", "item.iceandfire.banner_pattern_fire_head": "<PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_fire_head.desc": "<PERSON><PERSON><PERSON> <PERSON>", "item.iceandfire.banner_pattern_ice_head": "<PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_ice_head.desc": "<PERSON><PERSON><PERSON> <PERSON>", "item.iceandfire.banner_pattern_lightning_head": "<PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_lightning_head.desc": "<PERSON><PERSON><PERSON> de Dragon Électrique", "item.iceandfire.banner_pattern_amphithere": "<PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_amphithere.desc": "Amphithère", "item.iceandfire.banner_pattern_bird": "<PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_bird.desc": "Oiseaux", "item.iceandfire.banner_pattern_eye": "<PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_eye.desc": "<PERSON><PERSON>", "item.iceandfire.banner_pattern_fae": "<PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_fae.desc": "<PERSON><PERSON>", "item.iceandfire.banner_pattern_feather": "<PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_feather.desc": "Plume", "item.iceandfire.banner_pattern_gorgon": "<PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_gorgon.desc": "<PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_hippocampus": "<PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_hippocampus.desc": "Hippocampus", "item.iceandfire.banner_pattern_hippogryph_head": "<PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_hippogryph_head.desc": "<PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_mermaid": "<PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_mermaid.desc": "<PERSON><PERSON>", "item.iceandfire.banner_pattern_sea_serpent": "<PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_sea_serpent.desc": "<PERSON><PERSON>", "item.iceandfire.banner_pattern_weezer": "<PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_weezer.desc": "Weezer", "item.iceandfire.banner_pattern_troll": "<PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_troll.desc": "<PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_dread": "<PERSON><PERSON><PERSON>", "item.iceandfire.banner_pattern_dread.desc": "<PERSON><PERSON><PERSON><PERSON>", "entity.iceandfire.dragon_multipart": "<PERSON><PERSON>", "entity.iceandfire.multipart": "<PERSON><PERSON>", "entity.iceandfire.hydra_multipart": "Cou de l'Hydre", "entity.iceandfire.cylcops_multipart": "<PERSON><PERSON>", "entity.iceandfire.fire_dragon": "Dragon de Feu", "entity.iceandfire.ice_dragon": "<PERSON> de Glace", "entity.iceandfire.lightning_dragon": "Dragon Électrique", "entity.iceandfire.dragon_egg": "<PERSON><PERSON><PERSON>", "entity.iceandfire.dragon_arrow": "Flèche en Os de Dragon", "entity.iceandfire.dragon_skull": "<PERSON><PERSON><PERSON><PERSON>", "entity.iceandfire.fire_dragon_charge": "Boule de Feu de <PERSON>", "entity.iceandfire.ice_dragon_charge": "<PERSON>ule de Glace de Dragon", "entity.iceandfire.hippogryph": "Hippogryph", "entity.iceandfire.hippogryph_egg": "<PERSON><PERSON><PERSON><PERSON>", "entity.iceandfire.hippogryph.black": "<PERSON>pog<PERSON><PERSON>", "entity.iceandfire.hippogryph.brown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.iceandfire.hippogryph.chestnut": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.iceandfire.hippogryph.creamy": "Hippog<PERSON><PERSON>", "entity.iceandfire.hippogryph.dark_brown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.iceandfire.hippogryph.gray": "<PERSON>pog<PERSON><PERSON>", "entity.iceandfire.hippogryph.white": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.iceandfire.hippogryph.raptor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.iceandfire.hippogryph.alex": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.iceandfire.hippogryph.dodo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.iceandfire.stone_statue": "Statue en Roche", "entity.iceandfire.player": "<PERSON><PERSON><PERSON>", "entity.iceandfire.gorgon": "<PERSON><PERSON><PERSON>", "entity.iceandfire.pixie": "<PERSON><PERSON><PERSON>", "entity.iceandfire.pixie.type_0": "Fée Rose", "entity.iceandfire.pixie.type_1": "<PERSON><PERSON>", "entity.iceandfire.pixie.type_2": "<PERSON><PERSON>", "entity.iceandfire.pixie.type_3": "<PERSON><PERSON> Verte", "entity.iceandfire.pixie.type_4": "<PERSON><PERSON>", "entity.iceandfire.cyclops": "Cyclope", "entity.iceandfire.siren": "<PERSON><PERSON>", "entity.iceandfire.hippocampus": "Hippocampus", "entity.iceandfire.deathworm": "Ver des sables", "entity.iceandfire.deathworm_egg": "<PERSON><PERSON><PERSON> des sables", "entity.iceandfire.cockatrice": "<PERSON><PERSON>", "entity.iceandfire.cockatrice_egg": "<PERSON><PERSON><PERSON>", "entity.iceandfire.stymphalian_bird": "Oiseau du Lac Stymphale", "entity.iceandfire.stymphalian_feather": "Plume d'Oiseau du Lac Stymphale", "entity.iceandfire.stymphalian_arrow": "Flèche d'Oiseau du Lac Stymphale", "entity.iceandfire.troll": "Troll", "entity.iceandfire.amphithere": "Amphiptère", "entity.iceandfire.sea_serpent": "<PERSON><PERSON>", "entity.iceandfire.sea_serpent_bubbles": "<PERSON><PERSON>", "entity.iceandfire.sea_serpent_arrow": "<PERSON><PERSON><PERSON><PERSON> de Serpent <PERSON>", "entity.iceandfire.chain_tie": "<PERSON><PERSON><PERSON>", "entity.iceandfire.pixie_charge": "<PERSON><PERSON><PERSON>", "entity.iceandfire.tide_trident": "Tide Trident", "entity.iceandfire.mob_skull": "Crâne", "entity.iceandfire.dread_thrall": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.iceandfire.dread_ghoul": "Gouleffroie", "entity.iceandfire.dread_beast": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.iceandfire.dread_scuttler": "Myrmexffroie", "entity.iceandfire.dread_lich": "Licheffroie", "entity.iceandfire.dread_lich_skull": "Crâne de Licheffroie", "entity.iceandfire.dread_knight": "Chevaffroie", "entity.iceandfire.dread_horse": "Cheval de chevaffroie", "entity.iceandfire.black_frost_dragon": "Givremort", "entity.iceandfire.dread_queen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.iceandfire.hydra": "Hydre", "entity.iceandfire.hydra_breath": "Souffle de l'Hydre", "entity.iceandfire.hydra_arrow": "Flèche d'Hydre", "entity.minecraft.villager.scribe": "Scribe", "entity.iceandfire.ghost": "<PERSON><PERSON><PERSON>", "sea_serpent.blue": "Bleu", "sea_serpent.bronze": "Bronze", "sea_serpent.deepblue": "<PERSON><PERSON><PERSON>", "sea_serpent.green": "<PERSON>ert", "sea_serpent.purple": "Violet", "sea_serpent.red": "Rouge", "sea_serpent.teal": "<PERSON><PERSON><PERSON>", "dragon.red": "Rouge", "dragon.green": "<PERSON><PERSON><PERSON><PERSON>", "dragon.bronze": "Bronze", "dragon.gray": "<PERSON><PERSON>", "dragon.blue": "Bleu", "dragon.sapphire": "<PERSON><PERSON><PERSON>", "dragon.silver": "Argenté", "dragon.white": "<PERSON>", "dragon.electric": "Bleu Électrique", "dragon.amethyst": "<PERSON><PERSON><PERSON><PERSON>", "dragon.copper": "<PERSON>uiv<PERSON>", "dragon.black": "Noir", "dragon.fire": "<PERSON><PERSON>", "dragon.ice": "<PERSON>lace", "dragon.lightning": "Électrique", "dragon.name": "Nom : ", "dragon.unnamed": "Sans nom", "dragon.health": "Vie :", "dragon.stage": "Niveau ", "dragon.gender": "Genre : ", "dragon.gender.male": "<PERSON><PERSON><PERSON>", "dragon.gender.female": "<PERSON><PERSON><PERSON>", "dragon.hunger": "Faim : ", "dragon.owner": "Pro<PERSON><PERSON>é<PERSON> : ", "dragon.untamed": "Indompté", "dragon.armor_head": "<PERSON><PERSON><PERSON>", "dragon.armor_neck": "<PERSON><PERSON>", "dragon.armor_body": "Corps", "dragon.armor_tail": "Queue", "dragon.hatchtime": "<PERSON><PERSON><PERSON> dans :", "dragon.days.front": "(", "dragon.days.back": "Jours)", "dragon.command.sit": "Ce dragon se repose.", "dragon.command.stand": "Ce dragon erre.", "dragon.command.escort": "Ce dragon vous escorte.", "dragon.command.new_home": "La maison de ce dragon a été fixée en %d, %d, %d, %s.", "dragon.command.remove_home": "La maison de ce dragon à été réintialiser.", "hippogryph.command.sit": "Cet hippogriffe se repose.", "hippogryph.command.stand": "Cet hippogriffe erre.", "hippogryph.command.new_home": "La maison de cet hippogriffe a été fixée en %d, %d, %d.", "hippogryph.command.remove_home": "La maison de cet hippogriffe à été réintialiser.", "cockatrice.command.0": "Ce basilic patrouille.", "cockatrice.command.1": "Ce basilic se repose.", "cockatrice.command.2": "Ce basilic vous suit.", "cockatrice.command.3": "Ce basilic patrouille.", "cockatrice.command.remove_home": "La maison de ce basilic à été réintialiser.", "cockatrice.command.new_home": "La maison de ce basilic a été fixée en %d, %d, %d, %s.", "amphithere.command.new_home": "La position d'origine de cet amphithère a été fixée à %d, %d, %d.", "amphithere.command.0": "Cet amphithère est errant.", "amphithere.command.1": "Cet amphithère est assis.", "amphithere.command.2": "Cet amphithère vous suit.", "container.lectern.no_bestiary": "Impossible d'ajouter des pages", "container.lectern.costs": "Coûts:", "container.lectern.manuscript.many": "%s Manuscrits", "bestiary.contains": "Contient :", "bestiary.hold_shift": "Tenir §e[Maj]§r§7 pour voir le contenu", "bestiary.introduction": "Introduction", "bestiary.firedragon": "Dragon de feu", "bestiary.firedragonegg": "<PERSON><PERSON><PERSON> de feu", "bestiary.icedragon": "Dragon de glace", "bestiary.icedragonegg": "<PERSON><PERSON><PERSON> de <PERSON>", "bestiary.materials": "DragoMats", "bestiary.alchemy": "<PERSON><PERSON><PERSON>", "bestiary.villagers": "<PERSON><PERSON> neige<PERSON>", "bestiary.tameddragons": "Mon bébé dragon", "bestiary.hippogryph": "<PERSON>pog<PERSON><PERSON><PERSON>", "bestiary.gorgon": "<PERSON><PERSON><PERSON>", "bestiary.pixie": "Fée", "bestiary.cyclops": "Cyclopes", "bestiary.siren": "<PERSON><PERSON><PERSON>", "bestiary.hippocampus": "Hippocampus", "bestiary.deathworm": "Ver des sables", "bestiary.cockatrice": "<PERSON><PERSON>", "bestiary.stymphalianbird": "Oiseaux stymphaliens", "bestiary.troll": "Trolls", "bestiary.amphithere": "Amphithères", "bestiary.seaserpent": "<PERSON><PERSON>", "bestiary.dragonforge": "<PERSON><PERSON> de <PERSON>", "bestiary.hydra": "Hydre", "bestiary.dread_mobs": "Morts-veffroie", "bestiary.lightningdragon": "Dragon électrique", "bestiary.lightningdragonegg": "<PERSON><PERSON>f <PERSON>lect<PERSON>", "bestiary.ghost": "<PERSON><PERSON><PERSON>", "lectern.nopages": "Aucune nouvelle information ne peut être ajoutée.", "silvertools.hurt": "+2 de dégâts contre les morts-vivants", "dragon_sword_fire.hurt1": "+8 dégâts contre les Dragons de Glace", "dragon_sword_fire.hurt2": "Enflamme et renverse les cibles", "dragon_sword_ice.hurt1": "+8 dégâts contre les Dragons de Feu", "dragon_sword_ice.hurt2": "Gel les cibles", "dragon_sword_lightning.hurt1": "+4 dégâts contre les Dragons de Feu et les Dragons de Glace", "dragon_sword_lightning.hurt2": "Frappe les cibles avec la foudre", "message.iceandfire.dragonWander": "Ce dragon est maintenant en train d'errer.", "message.iceandfire.dragonFollow": "Ce dragon est maintenant en train de vous suivre.", "message.iceandfire.dragonSit": "Ce dragon est maintenant en train de s'asseoir", "message.iceandfire.dragonSleep": "Ce dragon est maintenant en train de dormir.", "message.iceandfire.knownAs": "Le dragon connu comme", "message.iceandfire.dragonTeleport": "Ce dragon a été téléporté.", "message.iceandfire.noDragonTeleport": "§cIl n'y a pas de dragon lié à ce cristal.", "message.iceandfire.dragonWanderName": "est maintenant en train d'errer..", "message.iceandfire.dragonFollowName": "est maintenant en train de vous suivre.", "message.iceandfire.dragonSitName": "est maintenant en train de s'asseoir.", "message.iceandfire.dragonSleepName": "est maintenant en train de dormir.", "message.iceandfire.dragonGrown": "Ce dragon a grandi jusqu'au Niveau", "message.iceandfire.dragonGrownEnd": "!", "message.iceandfire.dragonGrownName": "est passé au Niveau", "death.attack.dragon.0": "%s a été coupé en deux par un dragon", "death.attack.dragon.1": "%s a été déchiré en lambeaux par un dragon", "death.attack.dragon.2": "%s a été dévoré par un dragon", "death.attack.dragon.attacker_0": "%s a été divisé en deux par %s", "death.attack.dragon.attacker_1": "%s a été déchiré en lambeaux par %s", "death.attack.dragon.attacker_2": "%s a été dévoré par %s", "death.attack.dragon_fire.0": "%s a été transformé en KFC par un dragon", "death.attack.dragon_fire.1": "%s a été incinéré par un dragon", "death.attack.dragon_fire.2": "%s a été réduit en cendres par un dragon", "death.attack.dragon_fire.attacker_0": "%s a été transformé en KFC par un %s", "death.attack.dragon_fire.attacker_1": "%s a été incinéré par un %s", "death.attack.dragon_fire.attacker_2": "%s a été réduit en cendres par un %s", "death.attack.dragon_ice.0": "%s a été gelé par un dragon", "death.attack.dragon_ice.1": "%s a été transformé en glace par un dragon", "death.attack.dragon_ice.2": "%s a été mis en suspension d'activité par un dragon", "death.attack.dragon_ice.attacker_0": "%s a été gelé par un %s", "death.attack.dragon_ice.attacker_1": "%s a été transformé en glace par un %s", "death.attack.dragon_ice.attacker_2": "%s a été mis en suspension d'activité par un %s", "death.attack.gorgon.0": "%s a été transformé en pierre par une gorgone", "death.attack.gorgon.1": "%s a été transformé en gorgonzola par une gorgone.", "death.attack.gorgon.2": "%s a été solidifié par une gorgone", "death.attack.gorgon.attacker_0": "%s a été transformé en pierre par une %s", "death.attack.gorgon.attacker_1": "%s a été transformé en gorgonzola par une %s", "death.attack.gorgon.attacker_2": "%s a été solidifié par une %s", "death.attack.dragon_lightning.0": "%s a été foudroyé par un dragon", "death.attack.dragon_lightning.1": "%s a été dynamisé par un dragon", "death.attack.dragon_lightning.2": "%s a eu un coup de foudre en tentant de combattre un dragon", "death.attack.dragon_lightning.attacker_0": "%s a été dynamisé par un %s", "death.attack.dragon_lightning.attacker_1": "%s a été foudroyé par un %s", "death.attack.dragon_lightning.attacker_2": "%s a été électrifié par un %s", "key.dragon_strike": "Mo<PERSON><PERSON> de dragon", "key.dragon_fireAttack": "<PERSON><PERSON><PERSON> dragon", "key.dragon_down": "Descente du dragon/Bébé dragon Off", "key.dragon_change_view": "Changer la caméra à la 3e personne pour le dragon", "tc.aspect.mythus": "Créature mythologique, être légendaire, cryptique, et créature effrayante", "tc.aspect.draco": "Dragon", "advancements.iceandfire.root.title": "Ice and Fire", "advancements.iceandfire.root.description": "Entrez dans un monde de créatures magiques", "advancements.iceandfire.bestiary.title": "Le Livre des Monstres", "advancements.iceandfire.bestiary.description": "A partir de 3 manuscrits trouvés dans les donjons, créez un bestiaire", "advancements.iceandfire.lectern.title": "Vous voulez en savoir plus ?", "advancements.iceandfire.lectern.description": "Fabriquez un Pupitre de Bestiaire pour en savoir plus sur le monde de Ice and Fire", "advancements.iceandfire.kill_a_dragon.title": "Dragonslayer", "advancements.iceandfire.kill_a_dragon.description": "<PERSON>er un dragon de glace, de feu ou d'électricité", "advancements.iceandfire.dragon_egg.title": "Mère des Dragons", "advancements.iceandfire.dragon_egg.description": "Obt<PERSON>r un oeuf de dragon", "advancements.iceandfire.dragon_stick.title": "Commandant <PERSON>", "advancements.iceandfire.dragon_stick.description": "Fabriquez un Bâton de Commandement de Dragon pour contrôler vos dragons.", "advancements.iceandfire.dragon_flute.title": "Le Chant des Dragons", "advancements.iceandfire.dragon_flute.description": "<PERSON><PERSON><PERSON><PERSON> une Flûte de Dragon pour arrêter les dragons en fuite.", "advancements.iceandfire.dragon_horn.title": "Un Rugissement à Faire Dresser les Oreilles", "advancements.iceandfire.dragon_horn.description": "<PERSON><PERSON><PERSON><PERSON> une corne de dragon pour stocker les dragons", "advancements.iceandfire.dragon_meal.title": "Croissance du dragon", "advancements.iceandfire.dragon_meal.description": "<PERSON><PERSON>er un Repas de Dragon pour faire grandir rapidement les bébés dragons.", "advancements.iceandfire.dragonbone_tool.title": "Splintering II", "advancements.iceandfire.dragonbone_tool.description": "Fabriquer un outil en Os de Dragon", "advancements.iceandfire.dragonbone_sword_fire.title": "Une Épée <PERSON>", "advancements.iceandfire.dragonbone_sword_fire.description": "<PERSON><PERSON><PERSON> l'arme légendaire du dragon de feu, de glace ou d'électricité", "advancements.iceandfire.dragon_forge_brick.title": "Une autre Brique dans le Mur", "advancements.iceandfire.dragon_forge_brick.description": "<PERSON><PERSON>er une Brique de Forge de Dragon", "advancements.iceandfire.dragon_forge_core.title": "Le Coeur des Flammes", "advancements.iceandfire.dragon_forge_core.description": "<PERSON><PERSON><PERSON> un Coeur de Forge de Dragon", "advancements.iceandfire.dragonsteel.title": "Forgé dans les Flamme", "advancements.iceandfire.dragonsteel.description": "<PERSON><PERSON><PERSON> un lingot d'acier de dragon à partir de sang de dragon et de fer", "advancements.iceandfire.dragonsteel_weapon.title": "Évolution Draconique", "advancements.iceandfire.dragonsteel_weapon.description": "<PERSON><PERSON><PERSON> une arme à partir d'acier de dragon", "advancements.iceandfire.tame_hippogryph.title": "<PERSON>", "advancements.iceandfire.tame_hippogryph.description": "Apprivoisez un Hippogryphe en laissant tomber des pattes de lapin près de lui", "advancements.iceandfire.gorgon_head.title": "Le Choc des Titans", "advancements.iceandfire.gorgon_head.description": "Obt<PERSON><PERSON> la tête de gorgone, une arme qui transforme en pierre tous ceux qui la regardent", "advancements.iceandfire.tame_pixie.title": "Voici votre Homme", "advancements.iceandfire.tame_pixie.description": "Devenez ami avec une fée en déposant des gâteaux près d'elle", "advancements.iceandfire.jar_pixie.title": "Surfer <PERSON>", "advancements.iceandfire.jar_pixie.description": "Piégez une fée dans un bocal pour récolter de la poussière", "advancements.iceandfire.pixie_wand.title": "Où est Mon Esprit ?", "advancements.iceandfire.pixie_wand.description": "Créer l'arme légendaire des Fée", "advancements.iceandfire.rotten_egg.title": "<PERSON><PERSON><PERSON> jusq<PERSON>'<PERSON> la Moelle", "advancements.iceandfire.rotten_egg.description": "Obtenir un oeuf pourri", "advancements.iceandfire.tame_cockatrice.title": "Restez en Dehors de ma Périphérie !", "advancements.iceandfire.tame_cockatrice.description": "Apprivoisez un basilic à partir d'un oeuf ou en la regardant fixement à plusieurs reprises", "advancements.iceandfire.kill_cyclops.title": "Mon nom est Personne", "advancements.iceandfire.kill_cyclops.description": "Vaincre un Cyclope", "advancements.iceandfire.kill_siren.title": "Attache-moi au Mât", "advancements.iceandfire.kill_siren.description": "Résistez à son chant et tuez une sirène", "advancements.iceandfire.tame_hippocampus.title": "<PERSON>", "advancements.iceandfire.tame_hippocampus.description": "Apprivoiser un Hippocampus en le nourrissant d'éponges", "advancements.iceandfire.kill_deathworm.title": "Les Nageurs du Désert", "advancements.iceandfire.kill_deathworm.description": "Tuez un Ver des sables. Conseil de pro : ils adorent manger de la TNT !", "advancements.iceandfire.deathworm_egg.title": "<PERSON>", "advancements.iceandfire.deathworm_egg.description": "Obtenir un oeuf de Ver des sables", "advancements.iceandfire.kill_stymphalian_bird.title": "Herc était sur un rouleau !", "advancements.iceandfire.kill_stymphalian_bird.description": "<PERSON>er un Oiseau Stymphalien", "advancements.iceandfire.stymphalian_arrow.title": "Voler vers l'Avant", "advancements.iceandfire.stymphalian_arrow.description": "<PERSON><PERSON><PERSON><PERSON> une flèche en plumes d'Oiseau Stymhalien qui vole plus loin", "advancements.iceandfire.kill_troll.title": "Ils Ont un Troll des Cavernes !", "advancements.iceandfire.kill_troll.description": "<PERSON><PERSON> un Troll", "advancements.iceandfire.tame_amphithere.title": "Voltigeurs de la Jungle", "advancements.iceandfire.tame_amphithere.description": "Apprivoisez un amphithère en l'abattant et en le chevauchant", "advancements.iceandfire.kill_sea_serpent.title": "Les Monstres de la Mer", "advancements.iceandfire.kill_sea_serpent.description": "<PERSON><PERSON> un Serpent de <PERSON>.", "advancements.iceandfire.tide_trident.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>", "advancements.iceandfire.tide_trident.description": "<PERSON>er un trident de marée sur une entité", "advancements.iceandfire.dragonarmor.title": "Suit Up", "advancements.iceandfire.dragonarmor.description": "Obtenir une armure de dragon", "advancements.iceandfire.kill_hydra.title": "Les 12 travaux d'Hercule", "advancements.iceandfire.kill_hydra.description": "<PERSON><PERSON> une Hydre.", "advancements.iceandfire.kill_ghost.title": "Qui allez-vous <PERSON> ?", "advancements.iceandfire.kill_ghost.description": "<PERSON><PERSON>.", "iceandfire.fire_dragon_forge": "<PERSON>ge de Dragon de Feu", "iceandfire.ice_dragon_forge": "<PERSON><PERSON> de <PERSON>", "iceandfire.lightning_dragon_forge": "Forge de Dragon Électrique", "item.iceandfire.custom_banner.jei_desc": "Un motif de bannière unique !", "item.iceandfire.fire_dragon_blood.jei_desc": "Obtenu en interagissant avec un cadavre de dragon tout en tenant une bouteille vide.", "item.iceandfire.ice_dragon_blood.jei_desc": "Obtenu en interagissant avec un cadavre de dragon tout en tenant une bouteille vide.", "item.iceandfire.lightning_dragon_blood.jei_desc": "Obtenu en interagissant avec un cadavre de dragon tout en tenant une bouteille vide.", "item.iceandfire.dragonegg.jei_desc": "Placez-le dans une flamme nue s'il s'agit d'un dragon de feu, sous l'eau s'il s'agit d'un dragon de glace, ou sous la pluie s'il s'agit d'un dragon électrique et attendez longtemps. Le dragon se liera avec le joueur le plus proche.", "item.iceandfire.dragon_skull_fire.jei_desc": "Peut être placé sur le sol ou sur les murs comme un trophée.", "item.iceandfire.dragon_skull_ice.jei_desc": "Peut être placé sur le sol ou sur les murs comme un trophée.", "item.iceandfire.dragon_skull_lightning.jei_desc": "Peut être placé sur le sol ou sur les murs comme un trophée.", "item.iceandfire.hippogryph_skull.jei_desc": "Peut être placé sur le sol ou sur les murs comme un trophée.", "item.iceandfire.cyclops_skull.jei_desc": "Peut être placé sur le sol ou sur les murs comme un trophée.", "item.iceandfire.cockatrice_skull.jei_desc": "Peut être placé sur le sol ou sur les murs comme un trophée.", "item.iceandfire.stymphalian_skull.jei_desc": "Peut être placé sur le sol ou sur les murs comme un trophée.", "item.iceandfire.troll_skull.jei_desc": "Peut être placé sur le sol ou sur les murs comme un trophée.", "item.iceandfire.amphithere_skull.jei_desc": "Peut être placé sur le sol ou sur les murs comme un trophée.", "item.iceandfire.seaserpent_skull.jei_desc": "Peut être placé sur le sol ou sur les murs comme un trophée.", "item.iceandfire.hydra_skull.jei_desc": "Peut être placé sur le sol ou sur les murs comme un trophée.", "item.iceandfire.fire_stew.jei_desc": "Utilisé pour élever deux dragons mâle et femelle. La femelle créera ensuite un nid avec un oeuf.", "item.iceandfire.frost_stew.jei_desc": "Utilisé pour élever deux dragons mâle et femelle. La femelle créera ensuite un nid avec un oeuf.", "item.iceandfire.lightning_stew.jei_desc": "Utilisé pour élever deux dragons mâle et femelle. La femelle créera ensuite un nid avec un oeuf.", "material.dragonbone": "<PERSON><PERSON> <PERSON>", "modifier.splintering2": "Splintering II", "modifier.splintering2.desc": "Un souvenir pour vos ennemis !§r\\nFrappez-les davantage pour infliger plus de dégâts.", "modifier.fractured2": "Fractured II", "modifier.fractured2.desc": "Blessé!§r\\nLes dégâts de vos outils sont augmentés.", "modifier.splitting2": "Splitting II", "modifier.splitting2.desc": "Trois pour un!§r\\L'accélération soudaine de la libération d'une flèche la fait se diviser en trois.", "material.dragonsteel_fire": "Acier de Dragon de Feu", "material.dragonsteel_ice": "<PERSON><PERSON> <PERSON> Dragon <PERSON>lace", "material.dragonsteel_lightning": "Acier de Dragon Électrique", "modifier.hive_defender": "Défenseur de la Ruche", "modifier.hive_defender.desc": "Pour la Reine et la Colonie!§r\\nInflige +8 dégâts supplémentaires contre les vers des sables et +4 dégâts contre les non-arthropodes.", "modifier.flame2": "Inferno II", "modifier.flame2.desc": "Brûle bébé brûle!§r\\nEnflamme les ennemis pendant 15 secondes, avec des coups supplémentaires, et inflige des dégâts supplémentaires de flammes de dragon.", "modifier.frost2": "Blizzard II", "modifier.frost2.desc": "Glacé-glacé <PERSON>!§r\\nGèle les ennemis dans un bloc de glace pendant 15 secondes avec un effet de choc supplémentaire, et inflige des dégâts supplémentaires de gel de dragon.", "modifier.flame": "Inferno", "modifier.flame.desc": "Brûle bébé brûle!§r\\nEnflamme les ennemis pendant 15 secondes, avec des coups supplémentaires, et inflige des dégâts supplémentaires de flammes de dragon.", "modifier.frost": "Blizzard", "modifier.frost.desc": "Glacé-glacé <PERSON>!§r\\nGèle les ennemis dans un bloc de glace pendant 15 secondes avec un effet de choc supplémentaire, et inflige des dégâts supplémentaires de gel de dragon.", "material.stymph_feather": "Plume d'Oiseau Stymphalien", "modifier.antigravity": "Anti-Gravité", "modifier.antigravity.desc": "En avant!§r\\nLa flèche conserve sa hauteur et n'est pas affectée par la gravité pendant toute la durée du vol. Là où vous visez, elle vole.", "material.amphithere_feather": "Plume d'Amphithère", "modifier.arrow_knockback": "Contrecoup Volant", "modifier.arrow_knockback.desc": "Debout et au garde-à-vous!§r\\nLa flèche inflige un fort recul lorsqu'elle touche une entité.", "fluid.tconstruct.dragonsteel_fire": "Acier de Dragon de Feu Fondu", "fluid.tconstruct.dragonsteel_ice": "Acier de Dragon de Glace Fondu", "material.weezer": "Weezer", "modifier.sweater_song": "Undone", "modifier.sweater_song.desc": "\"Si tu veux détruire mon pull...\"§r\\nLes cibles portant une armure ont 30% de chances que leur armure soit retirée lors d'un coup critique.", "modifier.surf_wax_america": "Surf Wax America", "modifier.surf_wax_america.desc": "\"Tu prends ta voiture pour aller travailler, je prends ma planche... :\"§r\\nInflige 5 points de dégâts supplémentaires lorsque les joueurs sont sur une monture.", "modifier.in_the_garage": "<PERSON><PERSON> le Garage", "modifier.in_the_garage.desc": "\"Dans le garage où j'appartiens...\"§r\\nInflige 5 dégâts supplémentaires lorsque le joueur n'est pas en plein soleil.", "block.minecraft.banner.fire.white": "Dragon de Feu Blanc", "block.minecraft.banner.fire.orange": "Dragon de Feu Orange", "block.minecraft.banner.fire.magenta": "Dragon de Feu Ma<PERSON>a", "block.minecraft.banner.fire.light_blue": "Dragon de Feu Bleu Clair", "block.minecraft.banner.fire.yellow": "Dragon de Feu Jaune", "block.minecraft.banner.fire.lime": "Dragon de Feu Lime", "block.minecraft.banner.fire.pink": "<PERSON> de Feu <PERSON>", "block.minecraft.banner.fire.gray": "Dragon de Feu Gris", "block.minecraft.banner.fire.silver": "Dragon de Feu Argenté", "block.minecraft.banner.fire.cyan": "<PERSON> de <PERSON>", "block.minecraft.banner.fire.purple": "Dragon de Feu Violet", "block.minecraft.banner.fire.blue": "Dragon de Feu Bleu", "block.minecraft.banner.fire.brown": "Dragon de <PERSON>", "block.minecraft.banner.fire.green": "<PERSON> de Feu <PERSON>", "block.minecraft.banner.fire.red": "Dragon de Feu Rouge", "block.minecraft.banner.fire.black": "Dragon de Feu Noir", "block.minecraft.banner.ice.white": "Dragon de Glace Blanc", "block.minecraft.banner.ice.orange": "Dragon de Glace Orange", "block.minecraft.banner.ice.magenta": "Dragon de Glace Magenta", "block.minecraft.banner.ice.light_blue": "Dragon de Glace Bleu Clair", "block.minecraft.banner.ice.yellow": "Dragon de Glace Jaune", "block.minecraft.banner.ice.lime": "Dragon de Glace Lime", "block.minecraft.banner.ice.pink": "<PERSON> de Glace Rose", "block.minecraft.banner.ice.gray": "Dragon de Glace Gris", "block.minecraft.banner.ice.silver": "Dragon de Glace Argenté", "block.minecraft.banner.ice.cyan": "<PERSON> de Glace <PERSON>", "block.minecraft.banner.ice.purple": "Dragon de Glace Violet", "block.minecraft.banner.ice.blue": "Dragon de Glace Bleu", "block.minecraft.banner.ice.brown": "Dragon de Glace Mar<PERSON>", "block.minecraft.banner.ice.green": "<PERSON> de Glace <PERSON>ert", "block.minecraft.banner.ice.red": "Dragon de Glace Rouge", "block.minecraft.banner.ice.black": "Dragon de Glace Noir", "block.minecraft.banner.lightning.white": "Dragon Électrique Blanc", "block.minecraft.banner.lightning.orange": "Dragon Électrique Orange", "block.minecraft.banner.lightning.magenta": "Dragon Électrique <PERSON>", "block.minecraft.banner.lightning.light_blue": "Dragon Électrique <PERSON>", "block.minecraft.banner.lightning.yellow": "Dragon Électrique <PERSON>", "block.minecraft.banner.lightning.lime": "Dragon Électrique Lime", "block.minecraft.banner.lightning.pink": "Dragon Électrique <PERSON>", "block.minecraft.banner.lightning.gray": "Dragon Électrique <PERSON>", "block.minecraft.banner.lightning.silver": "Dragon Électrique Argenté", "block.minecraft.banner.lightning.cyan": "Dragon Électrique <PERSON>", "block.minecraft.banner.lightning.purple": "Dragon Électrique Violet", "block.minecraft.banner.lightning.blue": "Dragon Électrique B<PERSON>u", "block.minecraft.banner.lightning.brown": "Dragon Électrique <PERSON>", "block.minecraft.banner.lightning.green": "Dragon Électrique <PERSON>", "block.minecraft.banner.lightning.red": "Dragon Électrique Rouge", "block.minecraft.banner.lightning.black": "Dragon Électrique Noir", "block.minecraft.banner.fire_head.white": "<PERSON><PERSON><PERSON> <PERSON> de Feu Blanc", "block.minecraft.banner.fire_head.orange": "<PERSON><PERSON><PERSON> <PERSON> Dragon de Feu Orange", "block.minecraft.banner.fire_head.magenta": "<PERSON><PERSON><PERSON> <PERSON> de Feu <PERSON>", "block.minecraft.banner.fire_head.light_blue": "<PERSON><PERSON><PERSON> <PERSON> de Feu Bleu Clair", "block.minecraft.banner.fire_head.yellow": "<PERSON><PERSON><PERSON> <PERSON> de Feu <PERSON>", "block.minecraft.banner.fire_head.lime": "<PERSON><PERSON><PERSON> <PERSON> de Feu Lime", "block.minecraft.banner.fire_head.pink": "<PERSON><PERSON><PERSON> <PERSON>", "block.minecraft.banner.fire_head.gray": "<PERSON><PERSON><PERSON> <PERSON> de Feu G<PERSON>", "block.minecraft.banner.fire_head.silver": "<PERSON><PERSON><PERSON> <PERSON> Dragon de Feu Argenté", "block.minecraft.banner.fire_head.cyan": "<PERSON><PERSON><PERSON> <PERSON>", "block.minecraft.banner.fire_head.purple": "<PERSON><PERSON><PERSON> <PERSON> de Feu <PERSON>", "block.minecraft.banner.fire_head.blue": "<PERSON><PERSON><PERSON> <PERSON> de Feu Bleu", "block.minecraft.banner.fire_head.brown": "<PERSON><PERSON><PERSON> <PERSON>", "block.minecraft.banner.fire_head.green": "<PERSON><PERSON><PERSON> <PERSON>", "block.minecraft.banner.fire_head.red": "<PERSON><PERSON><PERSON> de Dragon de Feu Rouge", "block.minecraft.banner.fire_head.black": "<PERSON><PERSON><PERSON> <PERSON> de Feu Noir", "block.minecraft.banner.ice_head.white": "<PERSON><PERSON><PERSON> <PERSON> Dragon de Glace Blanc", "block.minecraft.banner.ice_head.orange": "<PERSON><PERSON><PERSON> <PERSON> Dragon de Glace Orange", "block.minecraft.banner.ice_head.magenta": "<PERSON><PERSON><PERSON> <PERSON> Dragon de Glace Magenta", "block.minecraft.banner.ice_head.light_blue": "<PERSON><PERSON><PERSON> <PERSON> de Glace Bleu Clair", "block.minecraft.banner.ice_head.yellow": "<PERSON><PERSON><PERSON> <PERSON> de Glace Jaune", "block.minecraft.banner.ice_head.lime": "<PERSON><PERSON><PERSON> <PERSON> de Glace Lime", "block.minecraft.banner.ice_head.pink": "<PERSON><PERSON><PERSON> <PERSON> G<PERSON>", "block.minecraft.banner.ice_head.gray": "<PERSON><PERSON><PERSON> <PERSON> de Glace G<PERSON>", "block.minecraft.banner.ice_head.silver": "<PERSON><PERSON><PERSON> <PERSON> Dragon de Glace Argenté", "block.minecraft.banner.ice_head.cyan": "<PERSON><PERSON><PERSON> <PERSON> G<PERSON>", "block.minecraft.banner.ice_head.purple": "<PERSON><PERSON><PERSON> <PERSON> de Glace <PERSON>", "block.minecraft.banner.ice_head.blue": "<PERSON><PERSON><PERSON> <PERSON> de Glace Bleu", "block.minecraft.banner.ice_head.brown": "<PERSON><PERSON><PERSON> <PERSON> G<PERSON>", "block.minecraft.banner.ice_head.green": "<PERSON><PERSON><PERSON> <PERSON> Glace <PERSON>", "block.minecraft.banner.ice_head.red": "<PERSON><PERSON><PERSON> <PERSON> Dragon de Glace Rouge", "block.minecraft.banner.ice_head.black": "<PERSON><PERSON><PERSON> <PERSON> de Glace Noir", "block.minecraft.banner.lightning_head.white": "<PERSON><PERSON>te de Dragon Électrique Blanc", "block.minecraft.banner.lightning_head.orange": "Tête de Dragon Électrique Orange", "block.minecraft.banner.lightning_head.magenta": "<PERSON><PERSON><PERSON> de Dragon Électrique Ma<PERSON>", "block.minecraft.banner.lightning_head.light_blue": "<PERSON><PERSON><PERSON> <PERSON> Dragon Électrique <PERSON>", "block.minecraft.banner.lightning_head.yellow": "<PERSON><PERSON><PERSON> de Dragon Électrique <PERSON>", "block.minecraft.banner.lightning_head.lime": "<PERSON><PERSON><PERSON> de Dragon Électrique Lim<PERSON>", "block.minecraft.banner.lightning_head.pink": "<PERSON><PERSON><PERSON> <PERSON> Dragon Électrique <PERSON>", "block.minecraft.banner.lightning_head.gray": "<PERSON><PERSON><PERSON> de Dragon Électrique <PERSON>", "block.minecraft.banner.lightning_head.silver": "<PERSON><PERSON><PERSON> de Dragon Électrique Argenté", "block.minecraft.banner.lightning_head.cyan": "<PERSON><PERSON><PERSON> <PERSON> Dragon Électrique <PERSON>", "block.minecraft.banner.lightning_head.purple": "<PERSON><PERSON><PERSON> de Dragon Électrique Violet", "block.minecraft.banner.lightning_head.blue": "<PERSON><PERSON><PERSON> de Dragon Électrique Bleu", "block.minecraft.banner.lightning_head.brown": "<PERSON><PERSON><PERSON> <PERSON> Dragon Électrique <PERSON>", "block.minecraft.banner.lightning_head.green": "<PERSON><PERSON><PERSON> <PERSON> Dragon Électrique <PERSON>", "block.minecraft.banner.lightning_head.red": "<PERSON><PERSON>te de Dragon Électrique Rouge", "block.minecraft.banner.lightning_head.black": "<PERSON><PERSON><PERSON> de Dragon Électrique Noir", "block.minecraft.banner.gorgon.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gorgon.orange": "Gorgone Orange", "block.minecraft.banner.gorgon.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gorgon.light_blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gorgon.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gorgon.lime": "Gorgone Lime", "block.minecraft.banner.gorgon.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gorgon.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gorgon.silver": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gorgon.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gorgon.purple": "Gorgone <PERSON>", "block.minecraft.banner.gorgon.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gorgon.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gorgon.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gorgon.red": "Gorgone Rouge", "block.minecraft.banner.gorgon.black": "Gorgone Noir", "block.minecraft.banner.fae.white": "<PERSON><PERSON>", "block.minecraft.banner.fae.orange": "Fae Orange", "block.minecraft.banner.fae.magenta": "<PERSON>ae <PERSON>a", "block.minecraft.banner.fae.light_blue": "<PERSON><PERSON>", "block.minecraft.banner.fae.yellow": "<PERSON><PERSON>", "block.minecraft.banner.fae.lime": "Fae <PERSON>e", "block.minecraft.banner.fae.pink": "<PERSON><PERSON>", "block.minecraft.banner.fae.gray": "<PERSON><PERSON>", "block.minecraft.banner.fae.silver": "Fae Argenté", "block.minecraft.banner.fae.cyan": "<PERSON><PERSON>", "block.minecraft.banner.fae.purple": "Fae <PERSON>", "block.minecraft.banner.fae.blue": "<PERSON>ae <PERSON>u", "block.minecraft.banner.fae.brown": "<PERSON><PERSON>", "block.minecraft.banner.fae.green": "<PERSON><PERSON>", "block.minecraft.banner.fae.red": "Fae Rouge", "block.minecraft.banner.fae.black": "Fae Noir", "block.minecraft.banner.hippogryph_head.white": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.hippogryph_head.orange": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.hippogryph_head.magenta": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.hippogryph_head.light_blue": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.hippogryph_head.yellow": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.hippogryph_head.lime": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.hippogryph_head.pink": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.hippogryph_head.gray": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.hippogryph_head.silver": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.hippogryph_head.cyan": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.hippogryph_head.purple": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.hippogryph_head.blue": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.hippogryph_head.brown": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.hippogryph_head.green": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.hippogryph_head.red": "<PERSON><PERSON><PERSON> d'Hip<PERSON>gry<PERSON>", "block.minecraft.banner.hippogryph_head.black": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.hippocampus.white": "Hippocampus <PERSON>", "block.minecraft.banner.hippocampus.orange": "Hippocampus <PERSON>", "block.minecraft.banner.hippocampus.magenta": "Hippocam<PERSON>", "block.minecraft.banner.hippocampus.light_blue": "Hippocampus Bleu Clair", "block.minecraft.banner.hippocampus.yellow": "Hippocam<PERSON>", "block.minecraft.banner.hippocampus.lime": "Hippocampus <PERSON>", "block.minecraft.banner.hippocampus.pink": "Hippocam<PERSON>", "block.minecraft.banner.hippocampus.gray": "Hip<PERSON>cam<PERSON>", "block.minecraft.banner.hippocampus.silver": "Hippocampus <PERSON>", "block.minecraft.banner.hippocampus.cyan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.hippocampus.purple": "Hippocampus <PERSON>", "block.minecraft.banner.hippocampus.blue": "Hippocampus <PERSON>u", "block.minecraft.banner.hippocampus.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.hippocampus.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.hippocampus.red": "Hippocampus Rouge", "block.minecraft.banner.hippocampus.black": "Hippocampus Noir", "block.minecraft.banner.mermaid.white": "Mermaid Blanc", "block.minecraft.banner.mermaid.orange": "Mermaid Orange", "block.minecraft.banner.mermaid.magenta": "Mermaid Magenta", "block.minecraft.banner.mermaid.light_blue": "Mermaid Bleu Clair", "block.minecraft.banner.mermaid.yellow": "Mermaid Jaune", "block.minecraft.banner.mermaid.lime": "Mermaid Lime", "block.minecraft.banner.mermaid.pink": "Mermaid Rose", "block.minecraft.banner.mermaid.gray": "Mermaid Gris", "block.minecraft.banner.mermaid.silver": "Mermaid Argenté", "block.minecraft.banner.mermaid.cyan": "Mermaid <PERSON>an", "block.minecraft.banner.mermaid.purple": "Mermaid Violet", "block.minecraft.banner.mermaid.blue": "Mermaid Bleu", "block.minecraft.banner.mermaid.brown": "Mermaid Marron", "block.minecraft.banner.mermaid.green": "Mermaid Vert", "block.minecraft.banner.mermaid.red": "Mermaid Rouge", "block.minecraft.banner.mermaid.black": "Mermaid Noir", "block.minecraft.banner.troll.white": "<PERSON><PERSON>", "block.minecraft.banner.troll.orange": "Troll Orange", "block.minecraft.banner.troll.magenta": "T<PERSON>", "block.minecraft.banner.troll.light_blue": "T<PERSON> <PERSON><PERSON>", "block.minecraft.banner.troll.yellow": "<PERSON><PERSON>", "block.minecraft.banner.troll.lime": "<PERSON><PERSON>", "block.minecraft.banner.troll.pink": "T<PERSON>", "block.minecraft.banner.troll.gray": "<PERSON><PERSON>", "block.minecraft.banner.troll.silver": "<PERSON><PERSON>", "block.minecraft.banner.troll.cyan": "<PERSON><PERSON>", "block.minecraft.banner.troll.purple": "Troll Violet", "block.minecraft.banner.troll.blue": "T<PERSON>", "block.minecraft.banner.troll.brown": "<PERSON><PERSON>", "block.minecraft.banner.troll.green": "<PERSON><PERSON>", "block.minecraft.banner.troll.red": "Troll Rouge", "block.minecraft.banner.troll.black": "Troll Noir", "block.minecraft.banner.eye.white": "<PERSON><PERSON>", "block.minecraft.banner.eye.orange": "<PERSON><PERSON>", "block.minecraft.banner.eye.magenta": "<PERSON><PERSON>", "block.minecraft.banner.eye.light_blue": "<PERSON><PERSON>", "block.minecraft.banner.eye.yellow": "<PERSON><PERSON>", "block.minecraft.banner.eye.lime": "<PERSON><PERSON>", "block.minecraft.banner.eye.pink": "<PERSON><PERSON>", "block.minecraft.banner.eye.gray": "<PERSON><PERSON>", "block.minecraft.banner.eye.silver": "<PERSON><PERSON>", "block.minecraft.banner.eye.cyan": "<PERSON><PERSON>", "block.minecraft.banner.eye.purple": "<PERSON><PERSON>", "block.minecraft.banner.eye.blue": "<PERSON><PERSON>", "block.minecraft.banner.eye.brown": "<PERSON><PERSON>", "block.minecraft.banner.eye.green": "<PERSON><PERSON>", "block.minecraft.banner.eye.red": "<PERSON>eil <PERSON>", "block.minecraft.banner.eye.black": "<PERSON><PERSON>", "block.minecraft.banner.feather.white": "Plume Blanche", "block.minecraft.banner.feather.orange": "Plume Orange", "block.minecraft.banner.feather.magenta": "Plume Ma<PERSON>a", "block.minecraft.banner.feather.light_blue": "Plume Bleu <PERSON>", "block.minecraft.banner.feather.yellow": "Plume Jaune", "block.minecraft.banner.feather.lime": "Plume Lime", "block.minecraft.banner.feather.pink": "P<PERSON> Rose", "block.minecraft.banner.feather.gray": "Plume Grise", "block.minecraft.banner.feather.silver": "Plume <PERSON>", "block.minecraft.banner.feather.cyan": "<PERSON><PERSON>", "block.minecraft.banner.feather.purple": "Plume Violette", "block.minecraft.banner.feather.blue": "Plume Bleu", "block.minecraft.banner.feather.brown": "P<PERSON>", "block.minecraft.banner.feather.green": "P<PERSON> Verte", "block.minecraft.banner.feather.red": "Plume Rouge", "block.minecraft.banner.feather.black": "Plume Noir", "block.minecraft.banner.bird.white": "Oiseaux Blanc", "block.minecraft.banner.bird.orange": "Oiseaux Orange", "block.minecraft.banner.bird.magenta": "Oiseaux Magenta", "block.minecraft.banner.bird.light_blue": "Oiseaux Bleu Clair", "block.minecraft.banner.bird.yellow": "Oiseaux Jaune", "block.minecraft.banner.bird.lime": "Oiseaux  Lime", "block.minecraft.banner.bird.pink": "<PERSON><PERSON><PERSON> Rose", "block.minecraft.banner.bird.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.bird.silver": "Oiseaux Argenté", "block.minecraft.banner.bird.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.bird.purple": "Oiseaux Violet", "block.minecraft.banner.bird.blue": "Oiseaux Bleu", "block.minecraft.banner.bird.brown": "<PERSON><PERSON><PERSON> Mar<PERSON>", "block.minecraft.banner.bird.green": "<PERSON><PERSON><PERSON> Vert", "block.minecraft.banner.bird.red": "Oiseaux Rouge", "block.minecraft.banner.bird.black": "Oiseaux Noir", "block.minecraft.banner.amphithere.white": "Amphithère Blanc", "block.minecraft.banner.amphithere.orange": "Amphithère Orange", "block.minecraft.banner.amphithere.magenta": "Amphithère Magenta", "block.minecraft.banner.amphithere.light_blue": "Amphithère Bleu Clair", "block.minecraft.banner.amphithere.yellow": "Amphithère Jaune", "block.minecraft.banner.amphithere.lime": "Amphithère Lime", "block.minecraft.banner.amphithere.pink": "Amphithère Rose", "block.minecraft.banner.amphithere.gray": "Amphith<PERSON>", "block.minecraft.banner.amphithere.silver": "Amphithère Argenté", "block.minecraft.banner.amphithere.cyan": "Amphith<PERSON>", "block.minecraft.banner.amphithere.purple": "Amphithère Violet", "block.minecraft.banner.amphithere.blue": "Amphithère Bleu", "block.minecraft.banner.amphithere.brown": "Amphith<PERSON>", "block.minecraft.banner.amphithere.green": "Am<PERSON><PERSON><PERSON>", "block.minecraft.banner.amphithere.red": "Amphithère Rouge", "block.minecraft.banner.amphithere.black": "Amphithère Noir", "block.minecraft.banner.sea_serpent.white": "<PERSON><PERSON>", "block.minecraft.banner.sea_serpent.orange": "<PERSON><PERSON> de <PERSON>", "block.minecraft.banner.sea_serpent.magenta": "<PERSON><PERSON> de <PERSON>", "block.minecraft.banner.sea_serpent.light_blue": "<PERSON><PERSON> de <PERSON>", "block.minecraft.banner.sea_serpent.yellow": "<PERSON><PERSON>", "block.minecraft.banner.sea_serpent.lime": "<PERSON><PERSON> de Me<PERSON>", "block.minecraft.banner.sea_serpent.pink": "<PERSON><PERSON>", "block.minecraft.banner.sea_serpent.gray": "<PERSON><PERSON>", "block.minecraft.banner.sea_serpent.silver": "<PERSON><PERSON>", "block.minecraft.banner.sea_serpent.cyan": "<PERSON><PERSON> de <PERSON>", "block.minecraft.banner.sea_serpent.purple": "<PERSON><PERSON>", "block.minecraft.banner.sea_serpent.blue": "<PERSON><PERSON>", "block.minecraft.banner.sea_serpent.brown": "<PERSON><PERSON>", "block.minecraft.banner.sea_serpent.green": "<PERSON><PERSON>", "block.minecraft.banner.sea_serpent.red": "<PERSON><PERSON>", "block.minecraft.banner.sea_serpent.black": "<PERSON><PERSON> de <PERSON>", "block.minecraft.banner.weezer.white": "We<PERSON>", "block.minecraft.banner.weezer.orange": "We<PERSON>", "block.minecraft.banner.weezer.magenta": "We<PERSON>", "block.minecraft.banner.weezer.light_blue": "Weezer <PERSON>", "block.minecraft.banner.weezer.yellow": "We<PERSON>", "block.minecraft.banner.weezer.lime": "We<PERSON>", "block.minecraft.banner.weezer.pink": "We<PERSON>", "block.minecraft.banner.weezer.gray": "<PERSON><PERSON>", "block.minecraft.banner.weezer.silver": "<PERSON><PERSON>", "block.minecraft.banner.weezer.cyan": "We<PERSON>", "block.minecraft.banner.weezer.purple": "Weezer <PERSON>", "block.minecraft.banner.weezer.blue": "We<PERSON>", "block.minecraft.banner.weezer.brown": "We<PERSON>", "block.minecraft.banner.weezer.green": "<PERSON><PERSON>", "block.minecraft.banner.weezer.red": "Weezer <PERSON>", "block.minecraft.banner.weezer.black": "Weezer <PERSON>", "block.minecraft.banner.dread.white": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.dread.orange": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.dread.magenta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.dread.light_blue": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.dread.yellow": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.dread.lime": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.dread.pink": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.dread.gray": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.dread.silver": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.dread.cyan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.dread.purple": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.dread.blue": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.dread.brown": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.dread.green": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.dread.red": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.dread.black": "<PERSON><PERSON><PERSON><PERSON>", "iceandfire.sound.subtitle.ghost_attack": "Un Fantôme attaque", "iceandfire.sound.subtitle.ghost_idle": "Un Fantôme expire", "iceandfire.sound.subtitle.ghost_hurt": "Un Fantôme est blessé", "iceandfire.sound.subtitle.ghost_die": "Un Fantôme meurt", "iceandfire.sound.subtitle.lightningdragon_breath": "Un Dragon Électrique crache de l'électricité", "iceandfire.sound.subtitle.lightningdragon_roar": "Un Dragon Électrique rugit", "iceandfire.sound.subtitle.lightningdragon_idle": "Un Dragon Électrique grogne", "iceandfire.sound.subtitle.lightningdragon_hurt": "Un Dragon Électrique est blessé", "iceandfire.sound.subtitle.lightningdragon_death": "Un Dragon Électrique meurt", "iceandfire.sound.subtitle.dread_ghoul_idle": "Une Goule de l'Épouvante grogne", "iceandfire.sound.subtitle.dread_lich_summon": "Une Liche de l'Épouvante invoque", "iceandfire.sound.subtitle.hydra_spit": "Une Hydre crache", "iceandfire.sound.subtitle.hydra_idle": "Une Hydre siffle", "iceandfire.sound.subtitle.hydra_hurt": "Une Hydre est blessé", "iceandfire.sound.subtitle.hydra_die": "Une Hydre meurt", "iceandfire.sound.subtitle.bestiary_page": "Feuilletage d'un bestiaire", "iceandfire.sound.subtitle.pixie_wand": "Une Baguette de Fée est utilisée", "iceandfire.sound.subtitle.sea_serpent_breath": "Un Serpent de Mer tire de l'eau", "iceandfire.sound.subtitle.sea_serpent_bite": "Un Serpent de Mer mord", "iceandfire.sound.subtitle.sea_serpent_roar": "Un Serpent de Mer rugit", "iceandfire.sound.subtitle.sea_serpent_idle": "Un Serpent de Me<PERSON> siffle", "iceandfire.sound.subtitle.sea_serpent_hurt": "Un Serpent de Mer est blessé", "iceandfire.sound.subtitle.sea_serpent_die": "Un Serpent de Me<PERSON> meurt", "iceandfire.sound.subtitle.amphithere_idle": "Un Amphithère grogne", "iceandfire.sound.subtitle.amphithere_hurt": "Un Amphithère est blessé", "iceandfire.sound.subtitle.amphithere_die": "Un Amphithère meurt", "iceandfire.sound.subtitle.amphithere_bite": "Un Amphithère mord", "iceandfire.sound.subtitle.stymphalian_bird_idle": "Un Oiseau du Lac Stymphale piaille", "iceandfire.sound.subtitle.stymphalian_bird_attack": "Un Oiseau du Lac Stymphale attaque", "iceandfire.sound.subtitle.stymphalian_bird_hurt": "Un Oiseau du Lac Stymphale est blessé", "iceandfire.sound.subtitle.stymphalian_bird_die": "Un Oiseau du Lac Stymphale meurt", "iceandfire.sound.subtitle.cockatrice_cry": "Un Basilic crie", "iceandfire.sound.subtitle.cockatrice_hurt": "Un Basilic est blessé", "iceandfire.sound.subtitle.cockatrice_idle": "Un Basilic grogne", "iceandfire.sound.subtitle.cockatrice_die": "Un Basilic meurt", "iceandfire.sound.subtitle.troll_idle": "Un Troll grogne", "iceandfire.sound.subtitle.troll_roar": "Un Troll rugit", "iceandfire.sound.subtitle.troll_hurt": "Un Troll est blessé", "iceandfire.sound.subtitle.troll_die": "Un Troll meurt", "iceandfire.sound.subtitle.naga_attack": "Un Naga attaque", "iceandfire.sound.subtitle.naga_idle": "Un Naga grogne", "iceandfire.sound.subtitle.naga_hurt": "Un Naga est blessé", "iceandfire.sound.subtitle.naga_die": "Un Naga meurt", "iceandfire.sound.subtitle.mermaid_idle": "Mermaid frolics", "iceandfire.sound.subtitle.mermaid_hurt": "Mermaid hurts", "iceandfire.sound.subtitle.mermaid_die": "Mermaid dies", "iceandfire.sound.subtitle.siren_song": "Une sirène chante", "iceandfire.sound.subtitle.deathworm_idle": "Un Ver des sables rugit", "iceandfire.sound.subtitle.deathworm_attack": "Un Ver des sables attaque", "iceandfire.sound.subtitle.deathworm_hurt": "Un Ver des sables est blessé", "iceandfire.sound.subtitle.deathworm_die": "Un Ver des sables meurt", "iceandfire.sound.subtitle.hippocampus_idle": "Un Hippocampus expire", "iceandfire.sound.subtitle.hippocampus_hurt": "Un Hippocampus est blessé", "iceandfire.sound.subtitle.hippocampus_die": "Un Hippocampus meurt", "iceandfire.sound.subtitle.cyclops_idle": "Un Cyclopes grogne", "iceandfire.sound.subtitle.cyclops_hurt": "Un Cyclopes est blessé", "iceandfire.sound.subtitle.cyclops_die": "Un Cyclopes meurt", "iceandfire.sound.subtitle.cyclops_bite": "Un Cyclopes mord", "iceandfire.sound.subtitle.cyclops_blinded": "Un Cyclopes pousse un cri de douleur", "iceandfire.sound.subtitle.gold_pile_step": "<PERSON><PERSON><PERSON> chose marche sur une pile de métaux", "iceandfire.sound.subtitle.gold_pile_break": "Quelque casse une pile de métaux", "iceandfire.sound.subtitle.pixie_idle": "Une Fée ricane", "iceandfire.sound.subtitle.pixie_hurt": "Une Fée est blessé", "iceandfire.sound.subtitle.pixie_die": "Une Fée meurt", "iceandfire.sound.subtitle.pixie_taunt": "Une Fée se moque", "iceandfire.sound.subtitle.gorgon_idle": "Une Gorgone ricane", "iceandfire.sound.subtitle.gorgon_hurt": "Une Gorgone est blessé", "iceandfire.sound.subtitle.gorgon_die": "Une Gorgone meurt", "iceandfire.sound.subtitle.gorgon_attack": "Une Gorgone attaque", "iceandfire.sound.subtitle.gorgon_petrify": "Une Gorgone hurle", "iceandfire.sound.subtitle.gorgon_turn_stone": "<PERSON><PERSON><PERSON> chose se transforme en pierre", "iceandfire.sound.subtitle.hippogryph_idle": "Un Hippogryphe braille", "iceandfire.sound.subtitle.hippogryph_hurt": "Un Hippogryphe est blessé", "iceandfire.sound.subtitle.hippogryph_die": "<PERSON> Hippogryphe meurt", "iceandfire.sound.subtitle.dragon_hatch": "Un dragon éclot", "iceandfire.sound.subtitle.firedragon_breath": "Un Dragon de Feu crache du feu", "iceandfire.sound.subtitle.icedragon_breath": "Un Dragon de Glace crache de la glace", "iceandfire.sound.subtitle.firedragon_idle": "Un Dragon de Feu grogne", "iceandfire.sound.subtitle.firedragon_hurt": "Un Dragon de Feu est blessé", "iceandfire.sound.subtitle.firedragon_death": "Un Dragon de Feu meurt", "iceandfire.sound.subtitle.firedragon_roar": "Un Dragon de Feu rugit", "iceandfire.sound.subtitle.icedragon_roar": "Un Dragon de Glace rugit", "iceandfire.sound.subtitle.icedragon_idle": "Un Dragon de Glace grogne", "iceandfire.sound.subtitle.icedragon_hurt": "Un Dragon de Glace est blessé", "iceandfire.sound.subtitle.icedragon_death": "Un Dragon de Glace meurt", "iceandfire.sound.subtitle.dragonegg_hatch": "L'oeuf de dragon éclot", "iceandfire.sound.subtitle.dragonflute": "Quelqu'un joue de la Flute de Dragon", "bestiary_gui": "Bestiaire", "item.iceandfire.dragonarmor_netherite": "<PERSON>ure de Dragon en netherite", "iceandfire.book.name": "Bestiaire", "iceandfire.book.landing": "Il s'agit d'un texte de renvoi.", "config.iceandfire.title": "§km§bIce §fAnd §6Fire §fConfig§km", "iceandfire.client": "Client", "iceandfire.customMainMenu": "IAF Menu principal personnalis<PERSON>", "iceandfire.dragonAuto3rdPerson": "Perspective à la 3e personne automatique", "iceandfire.category.dragon": "Dragon", "iceandfire.dragon.generate.skeletons": "Géneration des squelettes", "iceandfire.dragon.generate.skeletonChance": "Chance pour un squelettes", "iceandfire.dragon.generate.denGoldAmount": "Densité des piles d'or", "iceandfire.dragon.generate.oreRatio": "Ratio de minerai", "iceandfire.dragon.griefing": "Destruction", "iceandfire.dragon.tamedGriefing": "Destruction apprivoisée", "iceandfire.dragon.flapNoiseDistance": "Distance pour le bruit des ailes", "iceandfire.dragon.fluteDistance": "Distance d'affectation de la flute", "iceandfire.dragon.attackDamage": "Dégâts d'attaque", "iceandfire.dragon.attackDamageFire": "Dégâts d'attaque de feu", "iceandfire.dragon.attackDamageIce": "Dégâts d'attaque de givre", "iceandfire.dragon.attackDamageLightning": "Dégâts d'attaque électrique", "iceandfire.dragon.maxFlight": "Hauteur maximale de vol", "iceandfire.dragon.goldSearchLength": "Distance de recherche de l'or", "iceandfire.dragon.canHealFromBiting": "<PERSON><PERSON>t guérir en infligeant une morsure", "iceandfire.dragon.canDespawn": "Peut disparaître", "iceandfire.dragon.sleep": "Peut dormir", "iceandfire.dragon.digWhenStuck": "Creuser en cas de blocage", "iceandfire.dragon.breakBlockCooldown": "Temps entre chaque cassage de bloc", "iceandfire.dragon.targetSearchLength": "Distance de recherche de la cible", "iceandfire.dragon.wanderFromHomeDistance": "Errance du dragon à partir de la distance de la tannière", "iceandfire.dragon.hungerTickRate": "<PERSON><PERSON> de tic de la faim", "iceandfire.dragon.movedWronglyFix": "Résoudre le déplacement à tort", "iceandfire.dragon.blockBreakingDropChance": "Chance de butin pour un bloc cassé", "iceandfire.dragon.explosiveBreath": "Souffle explosif", "iceandfire.dragon.chunkLoadSummonCrystal": "Chargement du tronçon via cristal d'invocation", "iceandfire.dragon.dragonFlightSpeedMod": "Vitesse de vol du dragon", "iceandfire.dragon.loot.skull": "Butin : <PERSON><PERSON><PERSON><PERSON>", "iceandfire.dragon.loot.heart": "Butin : Coeur", "iceandfire.dragon.loot.blood": "Butin : <PERSON>", "iceandfire.dragon.maxHealth": "<PERSON><PERSON> maximale", "iceandfire.dragon.eggBornTime": "Temps de naissance des oeufs", "iceandfire.dragon.maxPathingNodes": "Noeuds d'acheminement maximum", "iceandfire.dragon.villagersFear": "<PERSON>eur des Villageois", "iceandfire.dragon.animalsFear": "Peur des animaux", "iceandfire.dragon.maxTamedDragonAge": "Âge max pour les dragons apprivoisés (en jours)", "iceandfire.category.hippogryphs": "<PERSON>pog<PERSON><PERSON><PERSON>", "iceandfire.hippogryphs.spawn": "Peut apparaître", "iceandfire.hippogryphs.spawnWeight": "Chance d'apparition", "iceandfire.hippogryphs.fightSpeedMod": "Vitesse de vol", "iceandfire.category.pixie": "Fée", "iceandfire.pixie.spawnChance": "Chance d'apparition", "iceandfire.pixie.size": "<PERSON><PERSON>", "iceandfire.pixie.stealItems": "Vol les items", "iceandfire.category.cyclops": "Cyclopes", "iceandfire.cyclops.spawnWanderingChance": "Chance d'apparition en errance", "iceandfire.cyclops.spawnCaveChance": "Chance d'apparition d'une cave", "iceandfire.cyclops.sheepSearchLength": "Rayon de recherche des moutons", "iceandfire.cyclops.maxHealth": "<PERSON><PERSON> maximale", "iceandfire.cyclops.attackDamage": "Dégâts d'attaque", "iceandfire.cyclops.biteDamage": "Dégâts de morsure", "iceandfire.cyclops.griefing": "Destruction", "iceandfire.category.siren": "<PERSON><PERSON>", "iceandfire.siren.maxHealth": "<PERSON><PERSON> maximale", "iceandfire.siren.shader": "Shader (nuanceur de couleurs)", "iceandfire.siren.maxSingTime": "Durée maximale de chant", "iceandfire.siren.timeBetweenSongs": "Temps entre les chants", "iceandfire.siren.spawnChance": "Chance d'apparition", "iceandfire.category.gorgon": "<PERSON><PERSON><PERSON>", "iceandfire.gorgon.maxHealth": "<PERSON><PERSON> maximale", "iceandfire.category.deathworm": "Ver des sables", "iceandfire.deathworm.targetSearchLength": "Taille de la zone de recherche d'une cible", "iceandfire.deathworm.maxHealth": "<PERSON><PERSON> maximale", "iceandfire.deathworm.attackDamage": "Dégâts d'attaque", "iceandfire.deathworm.attackMonsters": "Attaque les monstres", "iceandfire.deathworm.spawnChance": "Chance d'apparition", "iceandfire.category.cockatrice": "<PERSON><PERSON>", "iceandfire.cockatrice.chickenSearchLength": "Taille de la zone de recherche d'une poule", "iceandfire.cockatrice.eggChance": "Chance d'apparition lorsque d'un oeuf est jeté", "iceandfire.cockatrice.maxHealth": "<PERSON><PERSON> maximale", "iceandfire.cockatrice.chickensLayRottenEggs": "Les poules pondent des oeufs pourris", "iceandfire.cockatrice.spawn": "Peut apparaître", "iceandfire.cockatrice.spawnWeight": "Chance d'apparition", "iceandfire.category.bird": "Oiseau du Lac Stymphale", "iceandfire.bird.targetSearchLength": "Taille de la zone de recherche d'une cible", "iceandfire.bird.featherDropChance": "Chance de butin : Plume", "iceandfire.bird.featherAttackDamage": "Dégâts d'attaque des plumes", "iceandfire.bird.flockLength": "<PERSON><PERSON>", "iceandfire.bird.flightHeight": "Hauteur de vol", "iceandfire.bird.dataTagDrops": "Butins d'étiquetage de données", "iceandfire.bird.attackAnimals": "Attaque les animaux", "iceandfire.bird.spawnChance": "Chance d'apparition", "iceandfire.category.troll": "Troll", "iceandfire.troll.spawn": "Peut apparaître", "iceandfire.troll.spawnWeight": "Chance d'apparition", "iceandfire.troll.dropWeapon": "<PERSON><PERSON><PERSON> son arme", "iceandfire.troll.maxHealth": "<PERSON><PERSON> maximale", "iceandfire.troll.attackDamage": "Dégâts d'attaque", "iceandfire.category.amphithere": "Amphithère", "iceandfire.amphithere.spawn": "Peut apparaître", "iceandfire.amphithere.spawnWeight": "Chance d'apparition", "iceandfire.amphithere.villagerSearchLength": "Taille de la zone de recherche d'un villageois", "iceandfire.amphithere.tameTime": "Temps à apprivoiser", "iceandfire.amphithere.flightSpeed": "Vitesse de vol", "iceandfire.amphithere.maxHealth": "<PERSON><PERSON> maximale", "iceandfire.amphithere.attackDamage": "Dégâts d'attaque", "iceandfire.category.seaSerpent": "<PERSON><PERSON> de mer", "iceandfire.seaSerpent.spawnChance": "Chance d'apparition", "iceandfire.seaSerpent.griefing": "Déstruction", "iceandfire.seaSerpent.baseHealth": "<PERSON><PERSON>", "iceandfire.seaSerpent.attackDamage": "Attack Strength", "iceandfire.category.lich": "Licheffroie", "iceandfire.lich.spawn": "Peut apparaître", "iceandfire.lich.spawnWeight": "Nombre d'apparition", "iceandfire.lich.spawnChance": "Chance d'apparition", "iceandfire.category.hydra": "Hydre", "iceandfire.hydra.maxHealth": "<PERSON><PERSON> maximale", "iceandfire.hydra.spawnChance": "Chance d'apparition", "iceandfire.category.ghost": "<PERSON><PERSON><PERSON>", "iceandfire.ghost.generateGraveyards": "Génération des cimetières", "iceandfire.ghost.maxHealth": "<PERSON><PERSON> maximale", "iceandfire.ghost.attackDamage": "Dégâts d'attaque", "iceandfire.ghost.fromPlayerDeaths": "De la mort des joueurs", "iceandfire.category.armors": "Armures", "iceandfire.armors.dragonSteelBaseDamage": "Dégâts de base", "iceandfire.armors.dragonSteelBaseArmor": "Armure de base", "iceandfire.armors.dragonSteelBaseDurability": "Durabilité de base", "iceandfire.category.worldgen": "Génération du monde", "iceandfire.worldgen.dangerousDistanceLimit": "Limite de distance dangereuse", "iceandfire.worldgen.dangerousSeparationLimit": "Limite de séparation dangereuse", "iceandfire.worldgen.villagerHouseWeight": "Nombre de maisons du villageois", "iceandfire.worldgen.generateFireDragonCaveChance": "Chance de génération des cavernes de dragon de feu", "iceandfire.worldgen.generateFireDragonRoostChance": "Chance de génération des nids de dragon de feu", "iceandfire.worldgen.generateIceDragonCaveChance": "Chance de génération des cavernes de dragon de glace", "iceandfire.worldgen.generateIceDragonRoostChance": "Chance de génération des nids de dragon de glace", "iceandfire.worldgen.generateLightningDragonCaveChance": "Chance de génération des cavernes de dragon électrique", "iceandfire.worldgen.generateLightningDragonRoostChance": "Chance de génération des nids de dragon électrique", "iceandfire.worldgen.generateCyclopsCaveChance": "Chance de génération des caves de cyclopes", "iceandfire.worldgen.generateGorgonTempleChance": "Chance de génération des temples de gorgones", "iceandfire.worldgen.generateGraveYardChance": "Chance de génération des cimetières", "iceandfire.worldgen.generateHydraCaveChance": "Chance de génération des caves d'Hydre", "iceandfire.worldgen.generateMausoleumChance": "Chance de génération des mausolées", "iceandfire.worldgen.generateMyemexHiveDesertChance": "Chance de génération des colonies de myrmex du désert", "iceandfire.worldgen.generateMyemexHiveJungleChance": "Chance de génération des colonies de myrmex de la jungle", "iceandfire.worldgen.generatePixieVillageChance": "Chance de génération des villages de fées", "iceandfire.worldgen.generateSirenIslandChance": "Chance de génération d'un récif de sir<PERSON>", "iceandfire.misc.dreadQueenMaxHealth": "Reineffroie : <PERSON><PERSON> ma<PERSON>e", "iceandfire.category.hippocampus": "Hippocampus", "iceandfire.hippocampus.spawnChance": "Chance d'apparition d'un hippocampus ", "iceandfire.hippocampus.swimSpeedMod": "Vitesse de nage de l'Hippocampus", "iceandfire.dragon.maxBreathTimeMul": "Temps de respiration maximal", "iceandfire.category.misc": "Divers", "iceandfire.misc.allowAttributeOverriding": "Autoriser la superposition d'attributs", "config.jade.plugin_iceandfire.dragon": "Information de dragon", "config.jade.plugin_iceandfire.multipart": "Intégration multipartite", "emi.category.iceandfire.fire_forge": "Forge de Dragon de feu", "emi.category.iceandfire.ice_forge": "<PERSON>ge de <PERSON> de glace", "emi.category.iceandfire.lightning_forge": "Forge de Dragon électrique", "item.iceandfire.dragon_seeker": "<PERSON><PERSON><PERSON><PERSON> de dragon", "item.iceandfire.dragon_seeker.credit": "Porté du mod Ice and Fire : Dragonseeker par Syrikal", "item.iceandfire.dragon_seeker.tooltip": "Recherchez le dragon le plus proche à 150 pâtés de maisons", "item.iceandfire.epic_dragon_seeker": "<PERSON><PERSON><PERSON><PERSON> de dragon épique", "item.iceandfire.epic_dragon_seeker.tooltip": "Recherchez le dragon le plus proche à 200 pâtés de maisons", "item.iceandfire.legendary_dragon_seeker": "<PERSON>er<PERSON><PERSON> de dragon légendaire", "item.iceandfire.legendary_dragon_seeker.tooltip": "Recherchez le dragon le plus proche à 300 pâtés de maisons", "item.iceandfire.godly_dragon_seeker": "<PERSON><PERSON><PERSON><PERSON> de dragon divin", "item.iceandfire.godly_dragon_seeker.tooltip": "Recherchez le dragon le plus proche à 500 pâtés de maisons (Mode créatif uniquement)", "item.iceandfire.dragon_seeker.not_found": "Impossible de trouver un dragon", "item.iceandfire.dragon_seeker.found": "Un dragon a été trouvé", "item.iceandfire.dragon_seeker.found_location": "Le dragon le plus proche se trouve à ", "text.iceandfire.not_enable": "Activer cette fonction dans la configuration d'abord !", "iceandfire.misc.enableDragonSeeker": "<PERSON><PERSON> le chercheur de dragon", "screen.iceandfire.common.title": "Ice And Fire Common Settings", "screen.iceandfire.client.title": "Ice And Fire Client Settings", "item.iceandfire.cooked_rice_with_fire_dragon_meat": "Riz frit à la chair de dragon de feu", "item.iceandfire.cooked_rice_with_ice_dragon_meat": "Riz frit à la chair de dragon de glace", "item.iceandfire.cooked_rice_with_lightning_dragon_meat": "Riz frit à la chair de dragon électrique", "item.iceandfire.ghost_cream": "<PERSON><PERSON><PERSON>", "item.iceandfire.pixie_dust_milky_tea": "<PERSON><PERSON>é lacté de poudre de fée", "item.iceandfire.tooltip.require.delight": "Cet objet nécessite le mod Farmer's Delight pour être fabriqué.", "iceandfire.dragon.movedWronglyFix.comment": "<PERSON>uti<PERSON><PERSON>", "iceandfire.bird.dataTagDrops.comment": "<PERSON>uti<PERSON><PERSON>", "iceandfire.worldgen.villagerHouseWeight.comment": "<PERSON>uti<PERSON><PERSON>", "iceandfire.misc.allowAttributeOverriding.comment": "<PERSON>uti<PERSON><PERSON>", "block.iceandfire.dreadwood_leaves": "<PERSON><PERSON><PERSON> boiveff<PERSON>", "block.iceandfire.dreadwood_sapling": "<PERSON><PERSON><PERSON> de boiveff<PERSON>e", "biome.iceandfire.dread_forest": "<PERSON><PERSON><PERSON> d'e<PERSON><PERSON>e", "biome.iceandfire.dread_plain": "Plaine d'effroie", "warning.iceandfire.dreadland.not_complete": "Dreadland n'est pas encore terminé. Merci de ne pas signaler de bugs ou de perte de contenu !"}